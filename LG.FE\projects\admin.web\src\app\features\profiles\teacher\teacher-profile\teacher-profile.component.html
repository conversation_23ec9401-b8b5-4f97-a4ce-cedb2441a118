@defer (when !getUserProfileInfo$().loading && getUserProfileInfo$()?.data?.basicProfileInfoDto) {
    @let profileInfo = getUserProfileInfo$();
    <div class="profile-container">


        <div class="surface-section p-3 mb-3 border-round shadow-2">
            <!-- Header Bar -->
            <div class="flex justify-content-between align-items-center mb-3">
                <div class="flex align-items-center gap-2">
                    <p-button icon="pi pi-arrow-left" (click)="this.generalService.goBack()" styleClass="p-button-text p-button-sm" routerLink="/dashboard/teachers"></p-button>
                    <h2 class="m-0 text-xl">Teacher Profile</h2>
                </div>
                <div class="flex gap-2">
                    @if (profileInfo.data?.basicProfileInfoDto?.discriminator) {
                    <p-tag [value]="profileInfo.data.basicProfileInfoDto.discriminator" severity="info" [style]="{'font-size': '0.75rem'}"></p-tag>
                    }
                </div>
            </div>
    
            <!-- Compact Profile Info -->
            <div class="flex flex-column sm:flex-row align-items-center sm:align-items-start gap-3">
                <!-- Profile Photo -->
                @if (profileInfo.data?.basicProfileInfoDto?.userId) {
                <lib-prime-profile-photo-single
                    [userId]="profileInfo.data.basicProfileInfoDto.userId"
                    [width]="70"
                    [height]="70"
                    class="flex-shrink-0">
                </lib-prime-profile-photo-single>
                }
            
                <!-- Info Container -->
                <div class="flex-grow-1">
                    <div class="text-center sm:text-left mb-2">
                        <h3 class="text-xl font-medium m-0">
                            {{profileInfo.data?.basicProfileInfoDto?.firstName}} {{profileInfo.data?.basicProfileInfoDto?.lastName}}
                        </h3>
                    </div>
                    
                    <!-- Info Grid -->
                    <div class="grid m-0">
                        @if (profileInfo.data?.basicProfileInfoDto?.userId) {
                        <div class="col-12 sm:col-6 lg:col-4 p-0 sm:pr-2 mb-2">
                            <div class="surface-ground border-round p-2 flex align-items-center">
                                <i class="pi pi-id-card text-primary mr-2"></i>
                                <span class="text-600 text-sm text-overflow-ellipsis overflow-hidden">{{profileInfo.data.basicProfileInfoDto.userId}}</span>
                            </div>
                        </div>
                        }
                        @if (profileInfo.data?.userAddress?.city && profileInfo.data?.userAddress?.country) {
                        <div class="col-12 sm:col-6 lg:col-4 p-0 sm:pr-2 mb-2">
                            <div class="surface-ground border-round p-2 flex align-items-center">
                                <i class="pi pi-map-marker text-primary mr-2"></i>
                                <span class="text-600 text-sm">{{profileInfo.data.userAddress.city}}, {{profileInfo.data.userAddress.country}}</span>
                            </div>
                        </div>
                        }
                        @if (profileInfo.data?.basicProfileInfoDto?.dateOfRegistrationCompletion) {
                        <div class="col-12 sm:col-6 lg:col-4 p-0 sm:pr-2 mb-2">
                            <div class="surface-ground border-round p-2 flex align-items-center">
                                <i class="pi pi-calendar text-primary mr-2"></i>
                                <span class="text-600 text-sm">{{profileInfo.data.basicProfileInfoDto.dateOfRegistrationCompletion | date:'shortDate'}}</span>
                            </div>
                        </div>
                        }
                    </div>
                </div>
            </div>
    
        </div>
    </div>

    <div class="mt-3">
        <div class="surface-card p-0 border-round">
        <p-tabMenu [model]="tabItems" [activeItem]="activeItem" class="profile-tabs"></p-tabMenu>
        <router-outlet></router-outlet>
        </div>
    </div>
    
} @placeholder {
<div class="flex justify-content-center align-items-center min-h-screen">
    <p-progressSpinner />
</div>
}


