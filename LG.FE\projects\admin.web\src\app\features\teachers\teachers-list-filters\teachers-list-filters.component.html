<!-- Teachers List Filters Container -->
<div class="filters-container surface-card p-3 border-round">
  <!-- Filter Header -->
  <div class="filter-header flex justify-content-between align-items-center">
    <h4 class="text-900 font-medium m-0 mb-3">
      <i class="pi pi-filter text-primary mr-2"></i>Filters
    </h4>
    <p-button
      *ngIf="config.showToggleButton"
      pButton 
      type="button" 
      [icon]="isFilterOpen() ? 'pi pi-chevron-up' : 'pi pi-chevron-down'"
      class="p-button-rounded p-button-text p-button-sm filter-toggle-btn" 
      (click)="toggleFilters()">
    </p-button>
  </div>

  <!-- Filters Content -->
  <div id="filters-content-wrapper"
       [class.collapsed]="!isFilterOpen()"
       [class.expanded]="isFilterOpen()">
    <div id="filters-content">
      <div class="grid">
        
        <!-- Basic Filters -->
        <div class="col-12">
          <div class="flex flex-column gap-1">
            <!-- Gender Dropdown -->
            <div class="field">
              <label for="gender" class="block text-sm mb-2">Gender</label>
              <p-dropdown 
                id="gender" 
                [options]="enumDropdownOptionsService.genderOptions"
                [ngModel]="filterState.queryParams.gender"
                (ngModelChange)="onFilterChange('gender', $event)"
                placeholder="Select Gender" 
                optionLabel="label" 
                optionValue="value" 
                class="w-full">
              </p-dropdown>
            </div>
          </div>
        </div>

        <!-- Language Filters -->
        <div class="col-12">
          <div class="flex flex-column gap-1">
            <!-- Teaching Language Dropdown -->
            <div class="field">
              <label for="teachingLanguage" class="block text-sm mb-2">Teaching Language</label>
              <p-dropdown 
                id="teachingLanguage" 
                [options]="filterState.teachingLanguages"
                [ngModel]="filterState.queryParams.teachingLanguage"
                (ngModelChange)="onFilterChange('teachingLanguage', $event)"
                placeholder="Select Teaching Language" 
                optionLabel="name" 
                optionValue="id"
                class="w-full">
              </p-dropdown>
            </div>

            <!-- Native Language Dropdown -->
            <div class="field">
              <label for="speakingLanguage" class="block text-sm mb-2">Speaking Language</label>
              <p-dropdown 
                id="speakingLanguage" 
                [options]="filterState.nativeLanguages"
                [filter]="true"
                [ngModel]="filterState.queryParams.speakingLanguage"
                (ngModelChange)="onFilterChange('speakingLanguage', $event)"
                placeholder="Select Speaking Language" 
                class="w-full">
              </p-dropdown>
            </div>
          </div>
        </div>

        <!-- Status and Experience -->
        <div class="col-12">
          <div class="flex flex-column gap-1">
            <!-- Availability Status MultiSelect -->
            <div class="field">
              <label for="availabilityStatus" class="block text-sm mb-2">Availability Status</label>
              <p-multiSelect 
                id="availabilityStatus" 
                class="w-full"
                [options]="enumDropdownOptionsService.availabilityStatusEnumFlagsOptions"
                [ngModel]="selectedAvailabilityStatuses()"
                (ngModelChange)="onAvailabilityStatusChange($event)"
                defaultLabel="Select Availability Statuses" 
                optionLabel="label" 
                optionValue="value"
                display="chip">
              </p-multiSelect>
            </div>

            <!-- Show Only Active Toggle -->
            <div class="field-checkbox">
              <p-checkbox 
                id="includeBlocked" 
                [ngModel]="filterState.queryParams.includeBlocked"
                (ngModelChange)="onFilterChange('includeBlocked', $event)"
                [binary]="true">
              </p-checkbox>
              <label for="includeBlocked" class="ml-2">Include Blocked</label>
            </div>
          </div>
        </div>

        <!-- Teaching Experience -->
        <div class="col-12">
          <div class="flex flex-column gap-1">
            <!-- Teaching Ages Experience MultiSelect -->
            <div class="field">
              <label for="teachingAgesExperience" class="block text-sm mb-2">Teaching Ages Experience</label>
              <p-multiSelect 
                id="teachingAgesExperience" 
                class="w-full"
                [options]="enumDropdownOptionsService.teachingAgesExperienceEnumFlagsOptions"
                [ngModel]="selectedTeachingAgesExperience()"
                (ngModelChange)="onTeachingAgesExperienceChange($event)" 
                defaultLabel="Select Experience"
                optionLabel="label" 
                optionValue="value" 
                display="chip">
              </p-multiSelect>
            </div>

            <!-- Student Ages Preference MultiSelect -->
            <div class="field">
              <label for="teacherStudentAgesPreference" class="block text-sm mb-2">Student Ages Preference</label>
              <p-multiSelect 
                id="teacherStudentAgesPreference" 
                class="w-full"
                [options]="enumDropdownOptionsService.teacherStudentAgesPreferenceEnumFlagsOptions"
                [ngModel]="selectedTeacherStudentAgesPreference()"
                (ngModelChange)="onStudentAgesPreferenceChange($event)"
                defaultLabel="Select Preferences" 
                optionLabel="label" 
                optionValue="value"
                display="chip">
              </p-multiSelect>
            </div>
          </div>
        </div>

        <!-- Date Range -->
        <div class="col-12">
          <div class="flex flex-column gap-1">
            <label class="block text-sm mb-2">Registration Date Range</label>
            <div class="flex gap-2">
              <p-datepicker 
                id="approvedDateFrom" 
                [ngModel]="filterState.queryParams.approvedDateFrom"
                (ngModelChange)="onFilterChange('approvedDateFrom', $event)"
                dateFormat="yy-mm-dd" 
                placeholder="From"
                [appendTo]="'body'" 
                class="flex-1">
              </p-datepicker>
              <p-datepicker 
                id="approvedDateTo" 
                [ngModel]="filterState.queryParams.approvedDateTo"
                (ngModelChange)="onFilterChange('approvedDateTo', $event)"
                dateFormat="yy-mm-dd" 
                placeholder="To"
                [appendTo]="'body'" 
                class="flex-1">
              </p-datepicker>
            </div>
          </div>
        </div>

        <!-- Age Range Slider -->
        <div class="col-12">
          <div class="flex flex-column gap-1">
            <div class="field p-2">
              <label class="block text-sm mb-3">
                Student Ages Range ({{ studentAgesRange()[0] }} - {{ studentAgesRange()[1] }})
              </label>
              <div style="padding: 0 7px">
                <p-slider 
                  [ngModel]="studentAgesRange()" 
                  (ngModelChange)="onStudentAgesRangeChange($event)"
                  [range]="true" 
                  [min]="2" 
                  [max]="17" 
                  class="w-full">
                </p-slider>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="col-12">
      <div class="flex gap-2 justify-content-end mt-4">
        <button 
          pButton 
          type="button" 
          label="Reset" 
          class="p-button-outlined" 
          (click)="onReset()">
        </button>
        <button 
          pButton 
          type="button" 
          label="Apply Filters" 
          class="p-button-primary"
          (click)="onSearch()">
        </button>
      </div>
    </div>
  </div>
</div>
