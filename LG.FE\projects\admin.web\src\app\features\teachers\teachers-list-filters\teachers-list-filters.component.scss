// Teachers List Filters Component Styles
.filters-container {
  margin-bottom: 1rem;
  
  .filter-header {
    h4 {
      color: var(--text-color);
      font-weight: 600;
    }
    
    .filter-toggle-btn {
      width: 2rem;
      height: 2rem;
    }
  }

  // Filter content wrapper with animations
  #filters-content-wrapper {
    overflow: hidden;
    transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
    opacity: 1;

    &.collapsed {
      max-height: 0;
      opacity: 0;
    }

    &.expanded {
      max-height: 1000px; // Adjust based on content height
      opacity: 1;
    }
  }

  #filters-content {
    .field {
      margin-bottom: 1rem;
      
      label {
        font-weight: 500;
        color: var(--text-color-secondary);
      }
    }

    .field-checkbox {
      display: flex;
      align-items: center;
      margin-bottom: 1rem;
      
      label {
        font-weight: 500;
        color: var(--text-color-secondary);
      }
    }

    // Dropdown styling
    .p-dropdown {
      width: 100%;
      
      &.p-focus {
        box-shadow: 0 0 0 2px var(--primary-color-alpha);
      }
    }

    // MultiSelect styling
    .p-multiselect {
      width: 100%;
      
      &.p-focus {
        box-shadow: 0 0 0 2px var(--primary-color-alpha);
      }
      
      .p-multiselect-label {
        padding: 0.5rem 0.75rem;
      }
      
      .p-multiselect-token {
        background: var(--primary-color);
        color: var(--primary-color-text);
        border-radius: 1rem;
        padding: 0.25rem 0.5rem;
        margin: 0.125rem;
        font-size: 0.875rem;
      }
    }

    // DatePicker styling
    .p-datepicker {
      width: 100%;
      
      .p-inputtext {
        width: 100%;
        
        &:focus {
          box-shadow: 0 0 0 2px var(--primary-color-alpha);
        }
      }
    }

    // Slider styling
    .p-slider {
      .p-slider-range {
        background: var(--primary-color);
      }
      
      .p-slider-handle {
        background: var(--primary-color);
        border: 2px solid var(--primary-color);
        
        &:focus {
          box-shadow: 0 0 0 2px var(--primary-color-alpha);
        }
      }
    }

    // Checkbox styling
    .p-checkbox {
      .p-checkbox-box {
        &.p-highlight {
          background: var(--primary-color);
          border-color: var(--primary-color);
        }
        
        &:focus {
          box-shadow: 0 0 0 2px var(--primary-color-alpha);
        }
      }
    }
  }

  // Action buttons styling
  .p-button {
    &.p-button-primary {
      background: var(--primary-color);
      border-color: var(--primary-color);
      
      &:hover {
        background: var(--primary-color-dark);
        border-color: var(--primary-color-dark);
      }
    }
    
    &.p-button-outlined {
      color: var(--primary-color);
      border-color: var(--primary-color);
      
      &:hover {
        background: var(--primary-color);
        color: var(--primary-color-text);
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .filters-container {
    .grid {
      .col-12 {
        padding: 0.5rem;
      }
    }
    
    #filters-content {
      .field {
        margin-bottom: 0.75rem;
      }
      
      .flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem;
        
        &.justify-content-end {
          align-items: stretch;
        }
      }
    }
  }
}



// Focus and accessibility improvements
.filters-container {
  .p-component:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }
  
  // Ensure proper contrast for labels
  label {
    color: var(--text-color);
    
    &:hover {
      color: var(--text-color-secondary);
    }
  }
}

// Custom styling for specific filter elements
.filters-container {
  // Age range slider container
  .field.p-2 {
    background: var(--surface-50);
    border-radius: 0.5rem;
    border: 1px solid var(--surface-200);
    
    label {
      font-weight: 600;
      color: var(--text-color);
    }
  }
  
  // Date range container
  .flex.gap-2 {
    .p-datepicker {
      flex: 1;
    }
  }
}
