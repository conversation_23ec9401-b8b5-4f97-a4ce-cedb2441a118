import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TeachersListFiltersComponent, ITeachersFilterState, ITeachersFilterConfig } from './teachers-list-filters.component';
import { EnumDropdownOptionsService } from 'SharedModules.Library';

describe('TeachersListFiltersComponent', () => {
  let component: TeachersListFiltersComponent;
  let fixture: ComponentFixture<TeachersListFiltersComponent>;
  let mockEnumService: jasmine.SpyObj<EnumDropdownOptionsService>;

  beforeEach(async () => {
    const enumServiceSpy = jasmine.createSpyObj('EnumDropdownOptionsService', [], {
      genderOptions: [],
      availabilityStatusEnumFlagsOptions: [],
      teachingAgesExperienceEnumFlagsOptions: [],
      teacherStudentAgesPreferenceEnumFlagsOptions: []
    });

    await TestBed.configureTestingModule({
      imports: [
        TeachersListFiltersComponent,
        NoopAnimationsModule
      ],
      providers: [
        { provide: EnumDropdownOptionsService, useValue: enumServiceSpy }
      ]
    }).compileComponents();
    
    fixture = TestBed.createComponent(TeachersListFiltersComponent);
    component = fixture.componentInstance;
    mockEnumService = TestBed.inject(EnumDropdownOptionsService) as jasmine.SpyObj<EnumDropdownOptionsService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default filter state', () => {
    fixture.detectChanges();
    
    expect(component.isFilterOpen()).toBeTruthy();
    expect(component.studentAgesRange()).toEqual([2, 17]);
    expect(component.selectedAvailabilityStatuses()).toEqual([]);
  });

  it('should emit filterChanged when filter value changes', () => {
    spyOn(component.filterChanged, 'emit');
    
    component.onFilterChange('gender', 1);
    
    expect(component.filterChanged.emit).toHaveBeenCalledWith({
      filterName: 'gender',
      value: 1,
      resetPage: true
    });
  });

  it('should emit filterAction on search', () => {
    spyOn(component.filterAction, 'emit');
    
    component.onSearch();
    
    expect(component.filterAction.emit).toHaveBeenCalledWith({
      action: 'search',
      filters: jasmine.any(Object)
    });
  });

  it('should emit filterAction on reset', () => {
    spyOn(component.filterAction, 'emit');
    
    component.onReset();
    
    expect(component.filterAction.emit).toHaveBeenCalledWith({
      action: 'reset',
      filters: jasmine.any(Object)
    });
  });

  it('should toggle filter panel visibility', () => {
    spyOn(component.filterToggled, 'emit');
    
    const initialState = component.isFilterOpen();
    component.toggleFilters();
    
    expect(component.isFilterOpen()).toBe(!initialState);
    expect(component.filterToggled.emit).toHaveBeenCalledWith(!initialState);
  });

  it('should update student ages range', () => {
    const newRange = [5, 15];
    spyOn(component.filterChanged, 'emit');
    
    component.onStudentAgesRangeChange(newRange);
    
    expect(component.studentAgesRange()).toEqual(newRange);
    expect(component.filterChanged.emit).toHaveBeenCalledTimes(2); // min and max
  });

  it('should update availability statuses without immediate emission', () => {
    const statuses = [1, 2];
    
    component.onAvailabilityStatusChange(statuses);
    
    expect(component.selectedAvailabilityStatuses()).toEqual(statuses);
  });

  it('should convert array to flags correctly', () => {
    const values = [1, 2, 4];
    const result = (component as any).convertArrayToFlags(values);
    
    expect(result).toBe(7); // 1 | 2 | 4 = 7
  });

  it('should return null for empty array in convertArrayToFlags', () => {
    const result = (component as any).convertArrayToFlags([]);
    
    expect(result).toBeNull();
  });

  it('should sync internal state with input state', () => {
    const newState: ITeachersFilterState = {
      queryParams: {} as any,
      studentAgesRange: [3, 16],
      selectedAvailabilityStatuses: [1],
      selectedTeachingAgesExperience: [2],
      selectedTeacherStudentAgesPreference: [4],
      teachingLanguages: [],
      nativeLanguages: [],
      isFilterOpen: false
    };

    component.filterState = newState;
    component.ngOnChanges();
    fixture.detectChanges();

    expect(component.isFilterOpen()).toBe(false);
    expect(component.studentAgesRange()).toEqual([3, 16]);
    expect(component.selectedAvailabilityStatuses()).toEqual([1]);
  });

  it('should display toggle button when configured', () => {
    const config: ITeachersFilterConfig = {
      showToggleButton: true
    };

    component.config = config;
    fixture.detectChanges();

    const toggleButton = fixture.debugElement.query(By.css('.filter-toggle-btn'));
    expect(toggleButton).toBeTruthy();
  });

  it('should hide toggle button when configured', () => {
    const config: ITeachersFilterConfig = {
      showToggleButton: false
    };

    component.config = config;
    fixture.detectChanges();

    const toggleButton = fixture.debugElement.query(By.css('.filter-toggle-btn'));
    expect(toggleButton).toBeFalsy();
  });
});
