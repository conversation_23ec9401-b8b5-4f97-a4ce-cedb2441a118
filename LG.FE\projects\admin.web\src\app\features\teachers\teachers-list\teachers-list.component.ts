import { CommonModule } from '@angular/common';
import {
  Component,
  OnInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  HostListener,
  ElementRef,
  inject,
  OnDestroy,
  signal,
  computed,
  ViewChild
} from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { TableModule } from 'primeng/table';
import {
  IGetTeachersRequest,
  IGetTeachersResponse,
  ITeachingLanguageDto,
  nameOf,
  IGetAllTeachingLanguagesResponse,
  TeachingLanguagesRoutes,
  ITeachers,
  ISearchTeacherDto,
  IAvailabilityStatusOptionsEnum,
  ITeacherStudentAgesExperienceEnum,
  EnumDropdownOptionsService,
  ITeacherStudentAgesPreferenceEnum,
  IEnumDropdownOptions,
  GeneralService,
  IDataGridFields,
  IGenderEnum,
  IDataGridResponse,
  IGetLanguagesResponse,
  LocationDataRoutes,
  IBasedDataGridRequest
} from 'SharedModules.Library';
import { DropdownModule } from "primeng/dropdown";
import { FormsModule } from "@angular/forms";
import { DatePickerModule } from "primeng/datepicker";
import { ChipModule } from 'primeng/chip';
import { MultiSelectModule } from 'primeng/multiselect';
import { CheckboxModule } from 'primeng/checkbox';
import { ButtonModule } from "primeng/button";
import { SliderModule } from 'primeng/slider';
import { TeacherListComponentHelperService } from '../../../shared/services/teacher-list-component-helper.service'
import { BaseDataGridComponent } from "../../../shared/components/BaseDataGrid/BaseDataGridComponent";
import { Subject, takeUntil } from 'rxjs';
import moment from "moment-timezone";
import { DataGridHeaderFooterComponent } from '../../../shared/components/data-grid-header-footer/data-grid-header-footer.component';

@Component({
  selector: 'app-teachers-list',
  standalone: true,
  imports: [
    CommonModule,
    TableModule,
    DropdownModule,
    FormsModule,
    DatePickerModule,
    MultiSelectModule,
    CheckboxModule,
    ButtonModule,
    SliderModule,
    ChipModule,
    DataGridHeaderFooterComponent
  ],
  templateUrl: './teachers-list.component.html',
  styleUrls: ['./teachers-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TeachersListComponent extends BaseDataGridComponent implements OnInit, OnDestroy {
  teacherHelperService = inject(TeacherListComponentHelperService);
  enumDropdownOptionsService = inject(EnumDropdownOptionsService);
  generalService = inject(GeneralService);
  elementRef = inject(ElementRef);
  router = inject(Router);

  // Signal replacements for properties
  currentUrlParams = signal<Params>({});
  isFilterOpen = signal<boolean>(true);
  selectedColumns = signal<IDataGridFields[]>([]);

  // Signals for filter states
  studentAgesRange = signal<number[]>([2, 17]);
  selectedAvailabilityStatuses = signal<IAvailabilityStatusOptionsEnum[]>([]);
  selectedTeachingAgesExperience = signal<ITeacherStudentAgesExperienceEnum[]>([]);
  selectedTeacherStudentAgesPreference = signal<ITeacherStudentAgesPreferenceEnum[]>([]);
  teachingLanguages = signal<ITeachingLanguageDto[]>([]);
  nativeLanguages = signal<string[]>([]);

  // Query parameters as signal
  queryParams = signal<IGetTeachersRequest>(this.teacherHelperService.createDefaultTeachersRequest());

  // Teachers response data
  teachersResponse = signal<IDataGridResponse<ISearchTeacherDto>>(
    this.createEmptyDataGridResponse<ISearchTeacherDto>()
  );

  // Computed values
  totalRecords = computed(() => this.teachersResponse().totalRecords);
  currentPage = computed(() => this.teachersResponse().currentPage);
  pageSize = computed(() => this.queryParams().pageSize);

  private destroy$ = new Subject<void>();

  searchTeacherDtoFieldNames = nameOf<ISearchTeacherDto>();
  getTeachersRequestFieldNames = nameOf<IGetTeachersRequest>();
  constructor(
    route: ActivatedRoute,
    cdr: ChangeDetectorRef,
  ) {
    super(route, cdr);
  }

  ngOnInit(): void {
    // Initialize columns
    this.cols = this.teacherHelperService.initializeTableColumns();
    this.selectedColumns.set(this.cols);
    setTimeout(() => {
      this.toggleFilters(); // start close
    }, 100)

    // Subscribe to route query params for UI display
    this.route.queryParams.pipe(
      takeUntil(this.destroy$)
    ).subscribe(params => {
      this.currentUrlParams.set({ ...params });

      // If there are params in the URL, use them to initialize the request
      if (Object.keys(params).length > 0) {
        this.queryParams.set(this.teacherHelperService.mapQueryParamsToTeachersRequest(params));
      } else {
        // If no params, set defaults
        this.queryParams.set(this.teacherHelperService.createDefaultTeachersRequest());
        this.generalService.updateQueryParams(this.queryParams(), { replaceUrl: true });
      }

      // Initialize slider values
      this.studentAgesRange.set([
        this.queryParams().studentAgesMin || 2,
        this.queryParams().studentAgesMax || 17
      ]);

      // Extract flag values for multi-selects
      this.selectedAvailabilityStatuses.set(this.generalService.extractFlagsFromParams(
        params,
        this.getTeachersRequestFieldNames.availabilityStatus!,
        this.enumDropdownOptionsService.availabilityStatusEnumFlagsOptions,
      ));

      this.selectedTeachingAgesExperience.set(this.generalService.extractFlagsFromParams(
        params,
        this.getTeachersRequestFieldNames.teachingAgesExperience!,
        this.enumDropdownOptionsService.teachingAgesExperienceEnumFlagsOptions,
      ));

      this.selectedTeacherStudentAgesPreference.set(this.generalService.extractFlagsFromParams(
        params,
        this.getTeachersRequestFieldNames.teacherStudentAgesPreference!,
        this.enumDropdownOptionsService.teacherStudentAgesPreferenceEnumFlagsOptions,
      ));
    });

    // Load languages
    this.getTeachingLanguages();
    this.getNativeLanguages();


    // Initial data fetch with a short delay to ensure everything is initialized
    setTimeout(() => {
      this.fetchData();

      // After data is fetched, ensure the table's pagination reflects URL params
      setTimeout(() => {
        // If we have a pageNumber in our URL/query params, make sure the table shows that page
        const pageNumber = this.queryParams().pageNumber || 1;
        const pageSize = this.queryParams().pageSize || 10;

        if (this.table) {
          // Calculate the 'first' value that PrimeNG expects
          this.table.first = (pageNumber - 1) * pageSize;
          // Ensure the rows per page is correct
          this.table.rows = pageSize;
        }
      }, 0);


    }, 100);
  }

  ngOnDestroy(): void {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
    this.destroy$.next();
    this.destroy$.complete();
  }

  hasFilterInUrl(paramName: string): boolean {
    return !!this.currentUrlParams()[paramName] &&
      this.currentUrlParams()[paramName] !== '0' &&
      this.currentUrlParams()[paramName] !== 'false';
  }

  hasRangeFilterInUrl(minParam: string, maxParam: string, defaultMin = 2, defaultMax = 17): boolean {
    const params = this.currentUrlParams();
    const hasMin = !!params[minParam] && +params[minParam] !== defaultMin;
    const hasMax = !!params[maxParam] && +params[maxParam] !== defaultMax;
    return hasMin || hasMax;
  }

  hasAnyFiltersInUrl(): boolean {
    // Convert the typed field names to strings
    const params = this.currentUrlParams();
    const paginationParamNames: string[] = [
      this.getTeachersRequestFieldNames.pageNumber!,
      this.getTeachersRequestFieldNames.pageSize!,
      this.getTeachersRequestFieldNames.sortColumn!,
      this.getTeachersRequestFieldNames.sortDirection!
    ];

    return Object.keys(params).some(key =>
      !paginationParamNames.includes(key) &&
      params[key] !== '0' &&
      params[key] !== 'false'
    );
  }

  hasSortFilterInUrl(): boolean {
    const params = this.currentUrlParams();
    const sortColumn = params[this.getTeachersRequestFieldNames.sortColumn!];

    // Show sort filter if it's different from the default
    return !!(sortColumn && sortColumn !== TeacherListComponentHelperService.DEFAULT_SORT_COLUMN);
  }

  getSortColumnDisplayName(): string {
    const sortColumn = this.queryParams().sortColumn;

    // Find the display name from the table columns
    const column = this.cols.find(col => col.field === sortColumn);
    return column ? column.header : sortColumn || 'Unknown';
  }

  /**
   * Gets human-readable labels for different types of filter values from URL parameters
   */
  getFilterLabelFromUrl(filterType: 'gender' | 'language' | 'speakingLanguage' | 'availabilityStatus' | 'teachingAgesExperience' | 'studentAgesPreference', paramName: string): string {
    const value = this.currentUrlParams()[paramName];
    if (value === undefined || value === null || value === '0' || value === 'false') return '';

    switch (filterType) {
      case 'gender':
        const genderOption = this.enumDropdownOptionsService.genderOptions.find(
          g => g.value === +value
        );
        return genderOption ? genderOption.label : '';

      case 'language':
        if (!value) return '';
        const language = this.teachingLanguages().find(l => l.id === value);
        return language ? language.name : '';

      case 'speakingLanguage':
        if (!value) return '';
        // Direct return of the value since it's already the language name
        return value;

      case 'availabilityStatus':
        return this.getBitFlagLabels(
          this.enumDropdownOptionsService.availabilityStatusEnumFlagsOptions,
          +value
        );

      case 'teachingAgesExperience':
        return this.getBitFlagLabels(
          this.enumDropdownOptionsService.teachingAgesExperienceEnumFlagsOptions,
          +value
        );

      case 'studentAgesPreference':
        return this.getBitFlagLabels(
          this.enumDropdownOptionsService.teacherStudentAgesPreferenceEnumFlagsOptions,
          +value
        );

      default:
        return '';
    }
  }

  /**
   * Helper method to get labels from bit flag enum values
   */
  private getBitFlagLabels(optionsArray: IEnumDropdownOptions[], flagValue: number): string {
    if (!flagValue) return '';

    const matchingOptions = optionsArray
      .filter(option => (option.value & flagValue) === option.value && option.value !== 0)
      .map(option => option.label);

    return matchingOptions.join(', ');
  }

  onStudentAgesRangeChange(): void {
    // Update the query params when the slider values change
    this.queryParams.update(params => ({
      ...params,
      studentAgesMin: this.studentAgesRange()[0],
      studentAgesMax: this.studentAgesRange()[1]
    }));
  }

  onSearch(): void {
    this.saveScrollPosition();

    this.queryParams.update(params => ({
      ...params,
      availabilityStatus: this.generalService.convertArrayToFlags(this.selectedAvailabilityStatuses()),
      teachingAgesExperience: this.generalService.convertArrayToFlags(this.selectedTeachingAgesExperience()),
      teacherStudentAgesPreference: this.generalService.convertArrayToFlags(this.selectedTeacherStudentAgesPreference()),
      pageNumber: 1 // Reset to the first page whenever search is triggered
    }));

    // Update the query parameters in the URL
    this.generalService.updateQueryParams(this.queryParams(), { replaceUrl: true });

    // Fetch the filtered data
    this.fetchData();
    this.restoreScrollPosition();
  }

  resetFilters(): void {
    this.saveScrollPosition();

    // Reset to default values
    this.queryParams.set(this.teacherHelperService.createDefaultTeachersRequest());

    // Reset multi-select values
    this.selectedAvailabilityStatuses.set([]);
    this.selectedTeachingAgesExperience.set([]);
    this.selectedTeacherStudentAgesPreference.set([]);

    // Reset slider
    this.studentAgesRange.set([2, 17]);

    this.generalService.updateQueryParams(this.queryParams(), {
      preserveFragment: true,
      queryParamsHandling: '',
      replaceUrl: true
    });

    // Fetch data with clean parameters
    this.fetchData();
    this.restoreScrollPosition();
  }

  // Override the base class method to handle date formatting for API calls
  protected override cleanRequestForApi<T extends IBasedDataGridRequest>(request: T): Partial<T> {
    const cleanedRequest: Partial<T> = {};

    // Only include non-null and non-undefined values, with special handling for dates
    for (const [key, value] of Object.entries(request)) {
      if (value !== null && value !== undefined) {
        // Format Date objects to ISO strings for approvedDateFrom and approvedDateTo
        if ((key === 'approvedDateFrom' || key === 'approvedDateTo') && value instanceof Date) {
          const isoString = value.toISOString();
          console.log(`Formatting ${key} from Date object to ISO string:`, isoString);
          cleanedRequest[key as keyof T] = isoString as any;
        } else {
          cleanedRequest[key as keyof T] = value;
        }
      }
    }

    return cleanedRequest;
  }

  fetchData(): void {
    console.log("fetchData called with queryParams:", this.queryParams());

    // Use the generic method from the base class
    this.getDataTableData<IGetTeachersRequest, IGetTeachersResponse>(
      this.queryParams(),
      ITeachers.getTeachers,
      'Failed to load teachers'
    ).subscribe({
      next: (response: IGetTeachersResponse) => {
        console.log("API response received:", response);
        console.log("Total records:", response.totalRecords);
        console.log("Current page:", response.currentPage);
        console.log("Page size:", response.pageSize);
        console.log("Page data length:", response.pageData?.length || 0);

        // Update the response signal
        this.teachersResponse.set(response);
      },
      error: (error) => {
        console.error("Error handling in component level:", error);
        // The base method already handles setting empty response and loading state
      }
    });
  }


  private getTeachingLanguages(): void {
    this.handleApiService
      .getApiData<IGetAllTeachingLanguagesResponse>({
        url: TeachingLanguagesRoutes.getAllTeachingLanguages,
        method: 'GET',
      })
      .subscribe({
        next: (response: IGetAllTeachingLanguagesResponse) => {
          if (response) {
            this.teachingLanguages.set(response.teachingLanguages || []);
          } else {
            console.error('Failed to load teaching languages: response is undefined');
          }
        },
        error: (error: unknown) => {
          console.error('Failed to load teaching languages:', error);
        },
      });
  }

  private getNativeLanguages(): void {
    this.handleApiService
      .getApiData<IGetLanguagesResponse>({
        url: LocationDataRoutes.getLanguages,
        method: 'GET',
      })
      .subscribe({
        next: (response: IGetLanguagesResponse) => {
          if (response) {
            this.nativeLanguages.set(response.languages || []);
          } else {
            console.error('Failed to load native languages: response is undefined');
          }
        },
        error: (error: unknown) => {
          console.error('Failed to load native languages:', error);
        },
      });
  }

  /**
   * Removes a specific filter and updates the data
   */
  removeFilter(filterName: string, event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }

    this.saveScrollPosition();

    // Create a new params object with the updated values
    this.queryParams.update(params => {
      const updatedParams = { ...params };

      switch (filterName) {
        case this.getTeachersRequestFieldNames.searchTerm!:
          updatedParams.searchTerm = null;
          break;
        case this.getTeachersRequestFieldNames.gender:
          updatedParams.gender = 0;
          break;
        case this.getTeachersRequestFieldNames.teachingLanguage!:
          updatedParams.teachingLanguage = null;
          break;
        case this.getTeachersRequestFieldNames.speakingLanguage!:
          updatedParams.speakingLanguage = null;
          break;
        case this.getTeachersRequestFieldNames.includeBlocked!:
          updatedParams.includeBlocked = false;
          break;
        case this.getTeachersRequestFieldNames.availabilityStatus!:
          updatedParams.availabilityStatus = null;
          this.selectedAvailabilityStatuses.set([]);
          break;
        case this.getTeachersRequestFieldNames.teachingAgesExperience!:
          updatedParams.teachingAgesExperience = null;
          this.selectedTeachingAgesExperience.set([]);
          break;
        case this.getTeachersRequestFieldNames.teacherStudentAgesPreference!:
          updatedParams.teacherStudentAgesPreference = null;
          this.selectedTeacherStudentAgesPreference.set([]);
          break;
        case 'dateOfRegistration':
          updatedParams.approvedDateFrom = null;
          updatedParams.approvedDateTo = null;
          break;
        case 'studentAgesRange':
          updatedParams.studentAgesMin = 2;
          updatedParams.studentAgesMax = 17;
          this.studentAgesRange.set([2, 17]);
          break;
        case 'sortColumn':
          updatedParams.sortColumn = TeacherListComponentHelperService.DEFAULT_SORT_COLUMN;
          updatedParams.sortDirection = TeacherListComponentHelperService.DEFAULT_SORT_DIRECTION;
          break;
      }

      // Reset to the first page
      updatedParams.pageNumber = 1;

      return updatedParams;
    });

    // Update URL and fetch data
    this.generalService.updateQueryParams(this.queryParams(), { replaceUrl: true });
    this.fetchData();
    this.restoreScrollPosition();
  }

  private isClickFromModal(event: MouseEvent): boolean {
    if (!event || !event.target) return false;

    // Use Element.closest() to check if the click was in a modal element
    const element = event.target as HTMLElement;
    return !!element.closest('[aria-modal="true"]');
  }

  private isClickFromPrimeNGOverlay(event: MouseEvent): boolean {
    if (!event || !event.target) return false;

    const element = event.target as HTMLElement;

    // Check for PrimeNG overlay elements (datepicker, multiselect, dropdown, etc.)
    return !!(
      // Datepicker overlays
      element.closest('.p-datepicker') ||
      element.closest('.p-datepicker-panel') ||
      element.closest('.p-monthpicker') ||
      element.closest('.p-yearpicker') ||

      // MultiSelect overlays
      element.closest('.p-multiselect-panel') ||
      element.closest('.p-multiselect-items-wrapper') ||
      element.closest('.p-multiselect-item') ||
      element.closest('.p-multiselect-filter-container') ||

      // Dropdown overlays
      element.closest('.p-dropdown-panel') ||
      element.closest('.p-dropdown-items-wrapper') ||
      element.closest('.p-dropdown-item') ||

      // General PrimeNG overlays
      element.closest('.p-overlay') ||
      element.closest('.p-component-overlay') ||
      element.closest('.p-overlay-mask') ||
      element.closest('[data-pc-section="panel"]') ||
      element.closest('[data-pc-section="wrapper"]') ||
      element.closest('[data-pc-section="list"]') ||
      element.closest('[data-pc-section="item"]') ||
      element.closest('[data-pc-name="datepicker"]') ||
      element.closest('[data-pc-name="multiselect"]') ||
      element.closest('[data-pc-name="dropdown"]') ||

      // Check for any PrimeNG overlay that might be appended to body
      (element.closest('body') && (
        element.classList.contains('p-datepicker') ||
        element.classList.contains('p-datepicker-panel') ||
        element.classList.contains('p-multiselect-panel') ||
        element.classList.contains('p-dropdown-panel') ||
        element.classList.contains('p-overlay') ||
        element.hasAttribute('data-pc-name') ||
        element.hasAttribute('data-pc-section')
      )) ||

      // Fallback: check if any parent element has PrimeNG-related classes
      this.hasAnyPrimeNGParent(element)
    );
  }

  private hasAnyPrimeNGParent(element: HTMLElement): boolean {
    let currentElement: HTMLElement | null = element;

    while (currentElement && currentElement !== document.body) {
      // Check for any class that starts with 'p-' (PrimeNG convention)
      if (currentElement.className && typeof currentElement.className === 'string') {
        const classes = currentElement.className.split(' ');
        if (classes.some(cls => cls.startsWith('p-'))) {
          return true;
        }
      }

      // Check for PrimeNG data attributes
      if (currentElement.hasAttribute('data-pc-name') ||
          currentElement.hasAttribute('data-pc-section') ||
          currentElement.hasAttribute('data-pc-extend')) {
        return true;
      }

      currentElement = currentElement.parentElement;
    }

    return false;
  }


  @HostListener('document:click', ['$event'])
  onClickOutside(event: MouseEvent): void {
    // Only process if filters are currently open
    if (!this.isFilterOpen()) {
      return;
    }

    if (this.isClickFromModal(event)) {
      console.log('Ignoring click from modal component');
      return; // Don't close filters if the click came from a modal
    }

    if (this.isClickFromPrimeNGOverlay(event)) {
      console.log('Ignoring click from PrimeNG overlay (datepicker, dropdown, etc.)');
      return; // Don't close filters if the click came from a PrimeNG overlay
    }

    // Get references to our elements
    const filtersContainer = document.getElementById('filters-content-wrapper');
    const filterHeader = this.elementRef.nativeElement.querySelector('.filter-header');
    const appliedFiltersContainer = this.elementRef.nativeElement.querySelector('.applied-filters-container');

    // Check if the click was outside both elements
    if (filtersContainer && filterHeader) {
      const clickedInFilters = filtersContainer.contains(event.target as Node);
      const clickedInHeader = filterHeader.contains(event.target as Node);
      const clickedInAppliedFilters = appliedFiltersContainer && appliedFiltersContainer.contains(event.target as Node);

      // If click was outside all containers, close the filters
      if (!clickedInFilters && !clickedInHeader && !clickedInAppliedFilters) {
        console.log('Closing filters - click detected outside filter containers');
        // Use the existing toggle method to collapse the filter
        this.toggleFilters();
      } else {
        console.log('Click detected inside filter containers - keeping filters open');
      }
    }
  }

  toggleFilters(event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }

    this.isFilterOpen.update(value => !value);

    // Get the filters wrapper element
    const filtersWrapper = document.getElementById('filters-content-wrapper');

    if (filtersWrapper) {
      if (this.isFilterOpen()) {
        // Show with animation
        filtersWrapper.style.display = 'block';
        // Small delay to ensure display:block takes effect before starting animation
        setTimeout(() => {
          filtersWrapper.style.maxHeight = '100vh';
          filtersWrapper.style.opacity = '1';
        }, 10);
      } else {
        // Hide with animation
        filtersWrapper.style.maxHeight = '0';
        filtersWrapper.style.opacity = '0';
        // Wait for animation to finish before hiding
        setTimeout(() => {
          filtersWrapper.style.display = 'none';
        }, 300); // Match your CSS transition duration
      }
    }
  }

  // Page change handler - now simplified with signals
  // Page change handler - modified to prevent double triggering
  onPageChange(event: any): void {
    console.log('Page change event:', event);

    // Calculate the new page number (1-based)
    const newPageNumber = Math.ceil((event.first || 0) / (event.rows || 10)) + 1;
    const newPageSize = event.rows;

    // Get current pagination values
    const currentPageNumber = this.queryParams().pageNumber;
    const currentPageSize = this.queryParams().pageSize;

    // Skip if it's the same pagination configuration - prevents double triggering
    if (currentPageNumber === newPageNumber && currentPageSize === newPageSize) {
      console.log('Skipping redundant pagination event');
      return;
    }

    // Save scroll position
    this.saveScrollPosition();

    // Update the query params signal
    this.queryParams.update(params => ({
      ...params,
      pageNumber: newPageNumber,
      pageSize: newPageSize
    }));

    // Update URL and refresh data
    this.generalService.updateQueryParams(this.queryParams(), { replaceUrl: true });
    this.fetchData();
    this.restoreScrollPosition();
  }


  // Sort change handler - now simplified with signals
  onSortChange(event: any): void {
    console.log('Sort change event:', event);

    // First, check if this is a real sort change or just a reflection
    const currentSortColumn = this.queryParams().sortColumn;
    const currentSortDirection = this.queryParams().sortDirection;
    const newSortColumn = event.field;
    const newSortDirection = event.order === 1 ? 'asc' : 'desc';

    // Skip if it's the same sort configuration - prevents double triggering
    if (currentSortColumn === newSortColumn && currentSortDirection === newSortDirection) {
      console.log('Skipping redundant sort event');
      return;
    }

    // Save scroll position
    this.saveScrollPosition();

    // Update the query params signal
    this.queryParams.update(params => ({
      ...params,
      sortColumn: newSortColumn,
      sortDirection: newSortDirection
    }));

    // Update URL and refresh data
    this.generalService.updateQueryParams(this.queryParams(), { replaceUrl: true });
    this.fetchData();
    this.restoreScrollPosition();
  }


  // Update column selection (for table column visibility)
  onColumnsChange(cols: IDataGridFields[]): void {
    // Restore original order
    this.selectedColumns.set(
      this.cols.filter(col => cols.some(valCol => valCol.field === col.field))
    );
  }

  // Handle column reordering to keep header and footer in sync
  onColumnReorder(event: any): void {
    console.log('Column reorder event:', event);

    // PrimeNG onColReorder event provides the new column order
    // Update the selectedColumns signal with the new order to sync header and footer
    if (event.columns && Array.isArray(event.columns)) {
      // Filter out any non-column objects and ensure we only have valid IDataGridFields
      const reorderedColumns = event.columns.filter((col: any) =>
        col && typeof col === 'object' && col.field && col.header
      ) as IDataGridFields[];

      console.log('Updating column order:', reorderedColumns.map(c => c.field));
      this.selectedColumns.set([...reorderedColumns]);
    }
  }

  // Export table data
  exportTable(): void {
    this.teacherHelperService.exportTeacherTable(this.table, this.cols, this.teachersResponse());
  }

  // Add these methods to your TeachersListComponent class


  updateGender(value: number): void {
    this.queryParams.update(params => ({ ...params, gender: value }));
  }

  updateTeachingLanguage(value: string): void {
    console.log('Updating teaching language to:', value);
    this.queryParams.update(params => ({ ...params, teachingLanguage: value }));
  }

  updateNativeLanguage(value: string): void {
    this.queryParams.update(params => ({ ...params, speakingLanguage: value }));
  }

  updateShowOnlyActive(value: boolean): void {
    this.queryParams.update(params => ({ ...params, includeBlocked: value }));
  }

  updateDateOfRegistrationFrom(value: Date): void {
    console.log('Updating date of registration from:', value);
    this.queryParams.update(params => ({ ...params, approvedDateFrom: value }));
  }

  updateDateOfRegistrationTo(value: Date): void {
    this.queryParams.update(params => ({ ...params, approvedDateTo: value }));
  }

  private searchTimeout: any = null;

  updateSearchTerm(event: Event): void {
    const value = (event.target as HTMLInputElement).value;

    // Clear any existing timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Set a new timeout
    this.searchTimeout = setTimeout(() => {
      // Only proceed if value has actually changed
      if (this.queryParams().searchTerm !== value) {
        // Get current URL parameters
        const currentUrlParams = { ...this.currentUrlParams() };

        // Update only the search term and reset page to 1
        currentUrlParams[this.getTeachersRequestFieldNames.searchTerm!] = value || null;
        currentUrlParams[this.getTeachersRequestFieldNames.pageNumber!] = '1';

        // Remove the search term parameter if it's empty
        if (!value) {
          delete currentUrlParams[this.getTeachersRequestFieldNames.searchTerm!];
        }

        // Navigate with the updated URL parameters
        this.router.navigate([], {
          relativeTo: this.route,
          queryParams: currentUrlParams,
          replaceUrl: true
        });

        // Also update the component's internal state
        this.queryParams.update(params => ({
          ...params,
          searchTerm: value,
          pageNumber: 1
        }));

        // Fetch data with the updated parameters
        this.fetchData();
      }
    }, 400); // 400ms delay
  }

  clearSearchTerm(): void {
    // Create a fake input event
    const fakeEvent = {
      target: { value: '' } as HTMLInputElement
    } as unknown as Event;

    // Pass it to the existing update method
    this.updateSearchTerm(fakeEvent);
  }

  formatUtcDateToAdminLocalized(date: string | Date): string {
    if (!date) {
      return '';
    }
    //TODO: pass the admin timezone as parameter
    // Format using moment
    return moment.utc(date).tz('Europe/Athens').format('DD/MM/YYYY HH:mm');
  }

  goToTeacherOverview(id: string) {
    this.generalService.setPreviousUrl(this.router.url);
    this.router.navigate(['/dashboard/teachers/' + id + '/overview']);
  }
}
