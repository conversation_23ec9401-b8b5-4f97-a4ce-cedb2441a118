import {Directive, ViewChild, ChangeDetectorRef, inject, signal} from '@angular/core';
import {Table} from 'primeng/table';
import {ActivatedRoute} from '@angular/router';
import {Observable, Subject} from "rxjs";
import {
  HandleApiResponseService,
  IBasedDataGridRequest,
  IBasedDataGridResponse,
  IDataGridFields,
  IDataGridResponse,
  IEnumDropdownOptions
} from 'SharedModules.Library';

@Directive()
export class BaseDataGridComponent {
  @ViewChild('dt') table!: Table;

  constructor(
    protected route: ActivatedRoute,
    protected cdr: ChangeDetectorRef,
  ) {
  }

  handleApiService = inject(HandleApiResponseService);
  isLoading = signal<boolean>(false);
  cols: IDataGridFields[] = [];

  // Add property for rows per page options that components can use
  rowsPerPageOptions = signal<number[]>([10, 25, 50]);

  protected scrollPosition = 0;

  protected saveScrollPosition(): void {
    this.scrollPosition = window.scrollY;
    console.log('BaseDataGridComponent: Saving scroll position:', this.scrollPosition);
  }

  protected restoreScrollPosition(): void {
    // Use a slightly longer timeout to ensure DOM has fully updated
    setTimeout(() => {
      console.log('BaseDataGridComponent: Restoring to scroll position:', this.scrollPosition);
      window.scrollTo({
        top: this.scrollPosition,
        behavior: 'auto' // Use 'auto' instead of 'smooth' for more reliable positioning
      });
    }, 10);
  }

  // Helper function to clean the request object before sending to API
  protected cleanRequestForApi<T extends IBasedDataGridRequest>(request: T): Partial<T> {
    const cleanedRequest: Partial<T> = {};

    // Only include non-null and non-undefined values
    for (const [key, value] of Object.entries(request)) {
      if (value !== null && value !== undefined) {
        cleanedRequest[key as keyof T] = value;
      }
    }

    return cleanedRequest;
  }

  protected createEmptyDataGridResponse<T>(): IDataGridResponse<T> {
    return {
      currentPage: 0,
      pageSize: 0,
      totalRecords: 0,
      totalPages: 0,
      sortColumn: '',
      sortDirection: '',
      pageData: []
    };
  }

  /**
   * Generic method to fetch data table data from API and return an Observable
   *
   * @param request The request object containing filters and pagination
   * @param endpoint The API endpoint to call
   * @param errorPrefix Custom prefix for error messages
   * @returns Observable with the response
   * @template TRequest Type of the request object
   * @template TResponse Type of the response object
   */
  protected getDataTableData<
    TRequest extends IBasedDataGridRequest,
    TResponse extends IBasedDataGridResponse
  >(
    request: TRequest,
    endpoint: string,
    errorPrefix: string = 'Failed to load data'
  ): Observable<TResponse> {
    // Create a clean copy of the request without null values to prevent "null" strings
    const cleanRequest = this.cleanRequestForApi(request);

    // Create a subject to handle the API response
    const responseSubject = new Subject<TResponse>();

    // Set loading state
    this.isLoading.set(true);

    // Make the API call
    this.handleApiService
      .getApiData<TResponse>({url: endpoint, method: 'GET'}, cleanRequest)
      .subscribe({
        next: (response: TResponse) => {
          if (response) {
            // Emit the response
            responseSubject.next(response);

            // Update rows per page options based on the response
            this.updateRowsPerPageOptionsSignal(
              response.totalRecords,
              request.pageSize
            );
          } else {
            // Handle empty response by creating an empty one
            const emptyResponse = this.createEmptyDataGridResponse() as unknown as TResponse;
            responseSubject.next(emptyResponse);
            console.warn('Empty response received from server');
          }
          this.isLoading.set(false);
          // No need for markForCheck when using signals
        },
        error: (error: any) => {
          this.isLoading.set(false);

          // Set empty response on error to prevent undefined access
          const emptyResponse = this.createEmptyDataGridResponse() as unknown as TResponse;
          responseSubject.next(emptyResponse);

          // Handle specific error cases
          let errorMessage = `${errorPrefix}. `;

          if (error.status === 404) {
            errorMessage += 'The requested data could not be found.';
          } else if (error.messages && Array.isArray(error.messages)) {
            errorMessage += error.messages.join(', ');
          } else if (error.message) {
            errorMessage += error.message;
          } else {
            errorMessage += 'Please try again later.';
          }

          console.error(errorMessage, error);

          // No need for markForCheck when using signals

          // Complete with error
          responseSubject.error(error);
        },
        complete: () => {
          responseSubject.complete();
        }
      });

    return responseSubject.asObservable();
  }

  /**
   * Updates rows per page options based on total records in the response
   * Can be called automatically after getting data or manually
   *
   * @param totalRecords The total number of records
   * @param currentPageSize The current page size
   * @param customOptions Optional custom options array to use instead of default [10, 25, 50]
   */
  private updateRowsPerPageOptionsSignal(totalRecords: number, currentPageSize: number, customOptions?: number[]): void {
    // Use either custom options or default options
    let options = customOptions || [10, 25, 50];

    // Only add the total records option if:
    // 1. There are more than the highest standard option
    // 2. AND the totalRecords is not already included in the options
    if (totalRecords > 50 && !options.includes(totalRecords)) {
      options.push(totalRecords);
    }

    // Ensure there's always at least one option available
    if (options.length === 0) {
      options = [10, 25, 50];
    }

    // If current page size is not in options, add it
    if (!options.includes(currentPageSize)) {
      options.push(currentPageSize);
      // Sort the options numerically
      options.sort((a, b) => a - b);
    }

    // Set the new options - use set() method instead of direct assignment
    this.rowsPerPageOptions.set(options);
  }

}
