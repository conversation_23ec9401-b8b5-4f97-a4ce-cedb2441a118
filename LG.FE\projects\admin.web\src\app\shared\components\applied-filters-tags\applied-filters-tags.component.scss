// Import mixins for responsive design
@use 'mixins';

// Enterprise-grade color palette
:host {
  --primary-color: #6366f1;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --border-radius: 16px;
  --transition-duration: 0.2s;
  --shadow-subtle: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.applied-filters-container {
  border: 1px solid var(--surface-border);
  transition: all var(--transition-duration) ease-in-out;
  
  &:hover {
    box-shadow: var(--shadow-subtle);
  }
}

.applied-filters-header {
  .applied-filters-tags {
    // Responsive behavior for filter tags
    @include mixins.breakpoint(mobile) {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    @include mixins.breakpoint(tablet) {
      flex-direction: row;
      flex-wrap: wrap;
      align-items: center;
      gap: 0.5rem;
    }
  }
}

// Base filter chip styles
.filter-chip {
  position: relative;
  transition: all var(--transition-duration) ease-in-out;
  border: 1px solid transparent;
  min-height: 2.5rem;
  
  // Ensure proper touch targets for accessibility
  min-width: 44px;
  
  // Hover effects with subtle animation (max 1.02x scale as per user preference)
  &:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-subtle);
  }
  
  // Focus styles for accessibility
  &:focus-within {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }
  
  // Remove button styles
  .p-button {
    width: 1.5rem;
    height: 1.5rem;
    min-width: 1.5rem;
    
    &:hover {
      background-color: var(--danger-color);
      color: white;
    }
    
    &:focus {
      box-shadow: 0 0 0 2px var(--danger-color);
    }
  }
}

// Type-specific filter chip styles
.filter-chip--search {
  border-color: var(--primary-color);
  
  &:hover {
    background-color: rgba(99, 102, 241, 0.1);
  }
}

.filter-chip--sort {
  border-color: var(--success-color);
  
  &:hover {
    background-color: rgba(16, 185, 129, 0.1);
  }
}

.filter-chip--date {
  border-color: var(--warning-color);
  
  &:hover {
    background-color: rgba(245, 158, 11, 0.1);
  }
}

.filter-chip--select,
.filter-chip--multiselect {
  border-color: var(--primary-color);
  
  &:hover {
    background-color: rgba(99, 102, 241, 0.1);
  }
}

.filter-chip--range {
  border-color: var(--success-color);
  
  &:hover {
    background-color: rgba(16, 185, 129, 0.1);
  }
}

.filter-chip--boolean {
  border-color: var(--warning-color);
  
  &:hover {
    background-color: rgba(245, 158, 11, 0.1);
  }
}

.filter-chip--custom {
  border-color: var(--surface-400);
  
  &:hover {
    background-color: var(--surface-50);
  }
}

// Clear all button responsive behavior
.p-button-outlined.p-button-danger {
  @include mixins.breakpoint(mobile) {
    width: 100%;
    margin-top: 0.5rem;
  }

  @include mixins.breakpoint(tablet) {
    width: auto;
  }

  // Ensure proper contrast for accessibility
  &:hover {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
  }
  
  &:focus {
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.5);
  }
}

// Responsive header layout
.applied-filters-header {
  @include mixins.breakpoint(mobile) {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;

    .flex:first-child {
      width: 100%;
      flex-direction: column;
      align-items: flex-start;
      gap: 0.75rem;
    }
  }

  @include mixins.breakpoint(tablet) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}

// Header text and icon styling
.applied-filters-header h5 {
  color: var(--text-color);
  font-weight: 600;

  @include mixins.breakpoint(mobile) {
    font-size: 1rem;
  }

  @include mixins.breakpoint(tablet) {
    font-size: 1.125rem;
  }
}

// Icon styling
.text-primary {
  color: var(--primary-color) !important;
}

// Hidden filters indicator
.filter-chip:has(.pi-ellipsis-h) {
  background-color: var(--surface-200);
  border-color: var(--surface-300);
  cursor: default;
  
  &:hover {
    transform: none;
    box-shadow: none;
    background-color: var(--surface-200);
  }
}

// Animation for filter removal
.filter-chip {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .filter-chip {
    border-width: 2px;
    border-style: solid;
  }
  
  .applied-filters-container {
    border-width: 2px;
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .filter-chip,
  .applied-filters-container {
    transition: none;
  }
  
  .filter-chip:hover {
    transform: none;
  }
  
  .filter-chip {
    animation: none;
  }
}
