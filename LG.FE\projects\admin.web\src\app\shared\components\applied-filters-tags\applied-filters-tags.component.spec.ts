import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';
import { AppliedFiltersTagsComponent, IAppliedFilterTag, IAppliedFiltersConfig } from './applied-filters-tags.component';

describe('AppliedFiltersTagsComponent', () => {
  let component: AppliedFiltersTagsComponent;
  let fixture: ComponentFixture<AppliedFiltersTagsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AppliedFiltersTagsComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(AppliedFiltersTagsComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should not display when no filters and showWhenEmpty is false', () => {
    component.filters = [];
    component.showWhenEmpty = false;
    fixture.detectChanges();

    const container = fixture.debugElement.query(By.css('.applied-filters-container'));
    expect(container).toBeFalsy();
  });

  it('should display when filters are present', () => {
    const testFilters: IAppliedFilterTag[] = [
      {
        id: 'test-1',
        label: 'Test Filter',
        type: 'search',
        icon: 'pi pi-search'
      }
    ];

    component.filters = testFilters;
    fixture.detectChanges();

    const container = fixture.debugElement.query(By.css('.applied-filters-container'));
    expect(container).toBeTruthy();
  });

  it('should emit filterRemoved when remove button is clicked', () => {
    const testFilter: IAppliedFilterTag = {
      id: 'test-1',
      label: 'Test Filter',
      type: 'search',
      removable: true
    };

    component.filters = [testFilter];
    fixture.detectChanges();

    spyOn(component.filterRemoved, 'emit');

    const removeButton = fixture.debugElement.query(By.css('.p-button'));
    removeButton.nativeElement.click();

    expect(component.filterRemoved.emit).toHaveBeenCalledWith({
      filter: testFilter,
      event: jasmine.any(MouseEvent)
    });
  });

  it('should emit clearAllClicked when clear all button is clicked', () => {
    const testFilters: IAppliedFilterTag[] = [
      { id: 'test-1', label: 'Test Filter 1', type: 'search' },
      { id: 'test-2', label: 'Test Filter 2', type: 'select' }
    ];

    component.filters = testFilters;
    fixture.detectChanges();

    spyOn(component.clearAllClicked, 'emit');

    const clearAllButton = fixture.debugElement.query(By.css('.p-button-outlined.p-button-danger'));
    clearAllButton.nativeElement.click();

    expect(component.clearAllClicked.emit).toHaveBeenCalledWith(jasmine.any(MouseEvent));
  });

  it('should apply correct CSS classes based on filter type', () => {
    const testFilter: IAppliedFilterTag = {
      id: 'test-1',
      label: 'Search Filter',
      type: 'search'
    };

    const classes = component.getFilterTagClasses(testFilter);
    expect(classes).toContain('filter-chip--search');
    expect(classes).toContain('surface-100');
  });

  it('should limit visible filters when maxVisibleTags is set', () => {
    const testFilters: IAppliedFilterTag[] = [
      { id: 'test-1', label: 'Filter 1', type: 'search' },
      { id: 'test-2', label: 'Filter 2', type: 'select' },
      { id: 'test-3', label: 'Filter 3', type: 'date' }
    ];

    const config: IAppliedFiltersConfig = {
      maxVisibleTags: 2
    };

    component.filters = testFilters;
    component.config = config;
    fixture.detectChanges();

    expect(component.visibleFilters().length).toBe(2);
    expect(component.hiddenFiltersCount()).toBe(1);
  });

  it('should show hidden filters indicator when filters are hidden', () => {
    const testFilters: IAppliedFilterTag[] = [
      { id: 'test-1', label: 'Filter 1', type: 'search' },
      { id: 'test-2', label: 'Filter 2', type: 'select' },
      { id: 'test-3', label: 'Filter 3', type: 'date' }
    ];

    const config: IAppliedFiltersConfig = {
      maxVisibleTags: 2
    };

    component.filters = testFilters;
    component.config = config;
    fixture.detectChanges();

    const hiddenIndicator = fixture.debugElement.query(By.css('.pi-ellipsis-h'));
    expect(hiddenIndicator).toBeTruthy();
  });

  it('should not show remove button for non-removable filters', () => {
    const testFilter: IAppliedFilterTag = {
      id: 'test-1',
      label: 'Non-removable Filter',
      type: 'search',
      removable: false
    };

    component.filters = [testFilter];
    fixture.detectChanges();

    const removeButton = fixture.debugElement.query(By.css('.p-button'));
    expect(removeButton).toBeFalsy();
  });

  it('should track filters by id', () => {
    const testFilter: IAppliedFilterTag = {
      id: 'unique-test-id',
      label: 'Test Filter',
      type: 'search'
    };

    const trackResult = component.trackByFilterId(0, testFilter);
    expect(trackResult).toBe('unique-test-id');
  });
});
