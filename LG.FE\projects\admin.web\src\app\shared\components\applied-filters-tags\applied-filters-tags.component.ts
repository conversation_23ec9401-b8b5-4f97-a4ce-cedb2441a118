import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  signal,
  computed,
  ChangeDetectionStrategy,
  TemplateRef,
  ContentChild,
  OnInit,
  DestroyRef,
  inject
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ButtonModule } from 'primeng/button';

/**
 * Represents a filter tag with its display information and removal logic
 */
export interface IAppliedFilterTag {
  /** Unique identifier for the filter */
  id: string;
  /** Display label for the filter */
  label: string;
  /** Optional icon class (PrimeIcons) */
  icon?: string;
  /** Filter type for styling and categorization */
  type: FilterTagType;
  /** Data to pass back when removing the filter */
  removeData?: any;
  /** Whether this filter can be removed */
  removable?: boolean;
  /** Custom CSS classes */
  customClasses?: string;
}

/**
 * Event emitted when a filter tag is removed
 */
export interface IFilterTagRemoveEvent {
  /** The filter tag that was removed */
  filter: IAppliedFilterTag;
  /** Original event if available */
  event?: MouseEvent;
}

/**
 * Configuration for the applied filters component
 */
export interface IAppliedFiltersConfig {
  /** Show clear all button */
  showClearAll?: boolean;
  /** Clear all button label */
  clearAllLabel?: string;
  /** Clear all button icon */
  clearAllIcon?: string;
  /** Header text */
  headerText?: string;
  /** Header icon */
  headerIcon?: string;
  /** Maximum number of tags to show before collapsing */
  maxVisibleTags?: number;
  /** Enable responsive behavior */
  responsive?: boolean;
}

/**
 * Filter tag types for styling and categorization
 */
export type FilterTagType = 
  | 'search' 
  | 'sort' 
  | 'date' 
  | 'select' 
  | 'multiselect' 
  | 'range' 
  | 'boolean' 
  | 'custom';

/**
 * Template context for custom filter tag templates
 */
export interface IFilterTagTemplateContext {
  $implicit: IAppliedFilterTag;
  filter: IAppliedFilterTag;
  remove: (event?: MouseEvent) => void;
}

@Component({
  selector: 'app-applied-filters-tags',
  standalone: true,
  imports: [CommonModule, ButtonModule],
  templateUrl: './applied-filters-tags.component.html',
  styleUrl: './applied-filters-tags.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AppliedFiltersTagsComponent implements OnInit {
  private destroyRef = inject(DestroyRef);

  /**
   * Array of filter tags to display
   */
  @Input() filters: IAppliedFilterTag[] = [];

  /**
   * Configuration for the component
   */
  @Input() config: IAppliedFiltersConfig = {
    showClearAll: true,
    clearAllLabel: 'Clear All',
    clearAllIcon: 'pi pi-filter-slash',
    headerText: 'Applied Filters :',
    headerIcon: 'pi pi-filter',
    maxVisibleTags: undefined,
    responsive: true
  };

  /**
   * Whether to show the component when no filters are applied
   */
  @Input() showWhenEmpty: boolean = false;

  /**
   * Custom template for filter tags
   */
  @ContentChild('filterTagTemplate') filterTagTemplate?: TemplateRef<IFilterTagTemplateContext>;

  /**
   * Emitted when a single filter is removed
   */
  @Output() filterRemoved = new EventEmitter<IFilterTagRemoveEvent>();

  /**
   * Emitted when clear all is clicked
   */
  @Output() clearAllClicked = new EventEmitter<MouseEvent>();

  /**
   * Computed property to determine if component should be visible
   */
  readonly isVisible = computed(() => {
    const hasFilters = this.filters.length > 0;
    return hasFilters || this.showWhenEmpty;
  });

  /**
   * Computed property for visible filters (considering max visible limit)
   */
  readonly visibleFilters = computed(() => {
    const allFilters = this.filters;
    const maxVisible = this.config.maxVisibleTags;

    if (maxVisible && allFilters.length > maxVisible) {
      return allFilters.slice(0, maxVisible);
    }

    return allFilters;
  });

  /**
   * Computed property for hidden filters count
   */
  readonly hiddenFiltersCount = computed(() => {
    const allFilters = this.filters;
    const maxVisible = this.config.maxVisibleTags;

    if (maxVisible && allFilters.length > maxVisible) {
      return allFilters.length - maxVisible;
    }

    return 0;
  });

  /**
   * Computed property to check if clear all should be shown
   */
  readonly showClearAll = computed(() => {
    return this.config.showClearAll && this.filters.length > 0;
  });

  ngOnInit(): void {
    // Any initialization logic can go here
  }

  /**
   * Handles removal of a single filter tag
   */
  onFilterRemove(filter: IAppliedFilterTag, event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }

    if (filter.removable !== false) {
      this.filterRemoved.emit({
        filter,
        event
      });
    }
  }

  /**
   * Handles clear all button click
   */
  onClearAll(event: MouseEvent): void {
    event.stopPropagation();
    this.clearAllClicked.emit(event);
  }

  /**
   * Gets CSS classes for a filter tag based on its type
   */
  getFilterTagClasses(filter: IAppliedFilterTag): string {
    const baseClasses = 'filter-chip flex align-items-center gap-2 border-round-2xl px-3 py-2';
    const typeClasses = this.getTypeSpecificClasses(filter.type);
    const customClasses = filter.customClasses || '';
    
    return `${baseClasses} ${typeClasses} ${customClasses}`.trim();
  }

  /**
   * Gets type-specific CSS classes
   */
  private getTypeSpecificClasses(type: FilterTagType): string {
    switch (type) {
      case 'search':
        return 'filter-chip--search surface-100';
      case 'sort':
        return 'filter-chip--sort surface-100';
      case 'date':
        return 'filter-chip--date surface-100';
      case 'select':
        return 'filter-chip--select surface-100';
      case 'multiselect':
        return 'filter-chip--multiselect surface-100';
      case 'range':
        return 'filter-chip--range surface-100';
      case 'boolean':
        return 'filter-chip--boolean surface-100';
      case 'custom':
        return 'filter-chip--custom surface-100';
      default:
        return 'surface-100';
    }
  }

  /**
   * Track by function for filter tags
   */
  trackByFilterId(index: number, filter: IAppliedFilterTag): string {
    return filter.id;
  }
}
