import { Component, ViewChild, signal, computed, effect } from '@angular/core';
import { AppliedFiltersTagsComponent, IAppliedFilterTag, IFilterTagRemoveEvent } from '../applied-filters-tags.component';

/**
 * Example component demonstrating reactive usage of AppliedFiltersTagsComponent
 * This shows how to leverage the signal-based API for maximum reactivity
 */
@Component({
  selector: 'app-reactive-filters-example',
  standalone: true,
  imports: [AppliedFiltersTagsComponent],
  template: `
    <div class="reactive-filters-example p-4">
      <h3>Reactive Applied Filters Example</h3>
      
      <!-- Reactive Filter Component -->
      <app-applied-filters-tags
        #filtersComponent
        [filters]="currentFilters()"
        [config]="filterConfig()"
        (filterRemoved)="onFilterRemoved($event)"
        (clearAllClicked)="onClearAll()">
      </app-applied-filters-tags>
      
      <!-- Control Panel -->
      <div class="control-panel mt-4 p-3 surface-card border-round">
        <h4>Reactive Controls</h4>
        
        <div class="flex flex-wrap gap-2 mb-3">
          <button 
            class="p-button p-button-sm" 
            (click)="addSearchFilter()">
            Add Search Filter
          </button>
          
          <button 
            class="p-button p-button-sm p-button-secondary" 
            (click)="addDateFilter()">
            Add Date Filter
          </button>
          
          <button 
            class="p-button p-button-sm p-button-success" 
            (click)="toggleMaxVisible()">
            Toggle Max Visible ({{ filterConfig().maxVisibleTags || 'unlimited' }})
          </button>
          
          <button 
            class="p-button p-button-sm p-button-warning" 
            (click)="updateHeaderText()">
            Update Header Text
          </button>
          
          <button 
            class="p-button p-button-sm p-button-danger" 
            (click)="clearAllProgrammatically()">
            Clear All (Programmatic)
          </button>
        </div>
        
        <!-- Reactive Stats -->
        <div class="stats-panel p-2 surface-100 border-round">
          <p><strong>Total Filters:</strong> {{ totalFilters() }}</p>
          <p><strong>Visible Filters:</strong> {{ visibleFilters() }}</p>
          <p><strong>Hidden Filters:</strong> {{ hiddenFilters() }}</p>
          <p><strong>Component Visible:</strong> {{ isComponentVisible() ? 'Yes' : 'No' }}</p>
        </div>
      </div>
    </div>
  `
})
export class ReactiveFiltersExampleComponent {
  @ViewChild('filtersComponent') filtersComponent!: AppliedFiltersTagsComponent;

  // Reactive state with signals
  private filterCounter = signal(0);
  private maxVisibleToggle = signal(false);
  
  // Computed filters based on reactive state
  currentFilters = computed(() => {
    const counter = this.filterCounter();
    const filters: IAppliedFilterTag[] = [];
    
    // Add some example filters based on counter
    if (counter > 0) {
      filters.push({
        id: 'search-1',
        label: `Search: example-${counter}`,
        type: 'search',
        icon: 'pi pi-search'
      });
    }
    
    if (counter > 1) {
      filters.push({
        id: 'date-1',
        label: 'Date: Last 30 days',
        type: 'date',
        icon: 'pi pi-calendar'
      });
    }
    
    return filters;
  });

  // Reactive configuration
  filterConfig = computed(() => ({
    showClearAll: true,
    clearAllLabel: 'Clear All Filters',
    clearAllIcon: 'pi pi-filter-slash',
    headerText: `Applied Filters (${this.totalFilters()}):`,
    headerIcon: 'pi pi-filter',
    maxVisibleTags: this.maxVisibleToggle() ? 2 : undefined,
    responsive: true
  }));

  // Computed stats for demonstration
  totalFilters = computed(() => this.currentFilters().length);
  visibleFilters = computed(() => {
    const total = this.totalFilters();
    const maxVisible = this.filterConfig().maxVisibleTags;
    return maxVisible && total > maxVisible ? maxVisible : total;
  });
  hiddenFilters = computed(() => {
    const total = this.totalFilters();
    const visible = this.visibleFilters();
    return total - visible;
  });
  isComponentVisible = computed(() => this.totalFilters() > 0);

  constructor() {
    // Effect to demonstrate reactive logging
    effect(() => {
      const filters = this.currentFilters();
      const config = this.filterConfig();
      console.log('Reactive state changed:', { 
        filtersCount: filters.length, 
        maxVisible: config.maxVisibleTags,
        headerText: config.headerText
      });
    });
  }

  // Control methods
  addSearchFilter(): void {
    this.filterCounter.update(count => count + 1);
  }

  addDateFilter(): void {
    if (this.filterCounter() < 2) {
      this.filterCounter.set(2);
    }
  }

  toggleMaxVisible(): void {
    this.maxVisibleToggle.update(current => !current);
  }

  updateHeaderText(): void {
    // This will automatically update through the computed config
    this.filterCounter.update(count => count); // Trigger recomputation
  }

  clearAllProgrammatically(): void {
    this.filterCounter.set(0);
  }

  // Event handlers
  onFilterRemoved(event: IFilterTagRemoveEvent): void {
    console.log('Filter removed:', event.filter);
    
    // Remove specific filter based on ID
    if (event.filter.id === 'search-1') {
      this.filterCounter.set(0);
    } else if (event.filter.id === 'date-1') {
      this.filterCounter.set(1);
    }
  }

  onClearAll(): void {
    console.log('Clear all clicked');
    this.filterCounter.set(0);
  }
}
