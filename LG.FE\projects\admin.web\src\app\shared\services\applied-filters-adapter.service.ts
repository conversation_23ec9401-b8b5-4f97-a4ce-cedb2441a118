import { Injectable } from '@angular/core';
import { IAppliedFilterTag, FilterTagType } from '../components/applied-filters-tags/applied-filters-tags.component';
import { IGetTeachersRequest, nameOf } from 'SharedModules.Library';

/**
 * Service to adapt various filter request objects to IAppliedFilterTag format
 * This service provides a bridge between existing filter implementations and the new reusable component
 */
@Injectable({
  providedIn: 'root'
})
export class AppliedFiltersAdapterService {

  /**
   * Converts IGetTeachersRequest filters to IAppliedFilterTag array
   * @param request The teachers request object
   * @param currentUrlParams Current URL parameters for display values
   * @param labelResolvers Functions to resolve display labels for different filter types
   * @returns Array of filter tags
   */
  convertTeachersRequestToFilterTags(
    request: IGetTeachersRequest,
    currentUrlParams: Record<string, any>,
    labelResolvers: {
      getSortColumnDisplayName?: () => string;
      getFilterLabelFromUrl?: (filterType: string, paramName: string) => string;
      getDefaultSortColumn?: () => string;
      getDefaultSortDirection?: () => string;
    }
  ): IAppliedFilterTag[] {
    const filters: IAppliedFilterTag[] = [];
    const fieldNames = nameOf<IGetTeachersRequest>();

    // Sort Column Filter
    if (this.hasSortFilter(request, labelResolvers.getDefaultSortColumn?.(), labelResolvers.getDefaultSortDirection?.())) {
      filters.push({
        id: 'sortColumn',
        label: `Sort: ${labelResolvers.getSortColumnDisplayName?.() || request.sortColumn} (${request.sortDirection === 'asc' ? 'Ascending' : 'Descending'})`,
        icon: 'pi pi-sort-alt',
        type: 'sort',
        removeData: { filterName: 'sortColumn' }
      });
    }

    // Search Term Filter
    if (this.hasValue(request.searchTerm)) {
      filters.push({
        id: fieldNames.searchTerm!,
        label: request.searchTerm!,
        icon: 'pi pi-search',
        type: 'search',
        removeData: { filterName: fieldNames.searchTerm! }
      });
    }

    // Gender Filter
    if (this.hasValue(request.gender) && request.gender !== 0) {
      const genderLabel = labelResolvers.getFilterLabelFromUrl?.('gender', fieldNames.gender) || 'Gender';
      filters.push({
        id: fieldNames.gender,
        label: `Gender: ${genderLabel}`,
        icon: 'pi pi-user',
        type: 'select',
        removeData: { filterName: fieldNames.gender }
      });
    }

    // Teaching Language Filter
    if (this.hasValue(request.teachingLanguage)) {
      const languageLabel = labelResolvers.getFilterLabelFromUrl?.('language', fieldNames.teachingLanguage!) || request.teachingLanguage;
      filters.push({
        id: fieldNames.teachingLanguage!,
        label: `Teaching: ${languageLabel}`,
        icon: 'pi pi-globe',
        type: 'select',
        removeData: { filterName: fieldNames.teachingLanguage! }
      });
    }

    // Speaking Language Filter
    if (this.hasValue(request.speakingLanguage)) {
      const speakingLabel = labelResolvers.getFilterLabelFromUrl?.('speakingLanguage', fieldNames.speakingLanguage!) || request.speakingLanguage;
      filters.push({
        id: fieldNames.speakingLanguage!,
        label: `Speaking: ${speakingLabel}`,
        icon: 'pi pi-comments',
        type: 'select',
        removeData: { filterName: fieldNames.speakingLanguage! }
      });
    }

    // Include Blocked Filter
    if (this.hasValue(request.includeBlocked) && request.includeBlocked === true) {
      filters.push({
        id: fieldNames.includeBlocked!,
        label: 'Include Blocked: Yes',
        icon: 'pi pi-ban',
        type: 'boolean',
        removeData: { filterName: fieldNames.includeBlocked! }
      });
    }

    // Availability Status Filter
    if (this.hasValue(request.availabilityStatus)) {
      const statusLabel = labelResolvers.getFilterLabelFromUrl?.('availabilityStatus', fieldNames.availabilityStatus!) || 'Status';
      filters.push({
        id: fieldNames.availabilityStatus!,
        label: `Status: ${statusLabel}`,
        icon: 'pi pi-clock',
        type: 'multiselect',
        removeData: { filterName: fieldNames.availabilityStatus! }
      });
    }

    // Teaching Ages Experience Filter
    if (this.hasValue(request.teachingAgesExperience)) {
      const experienceLabel = labelResolvers.getFilterLabelFromUrl?.('teachingAgesExperience', fieldNames.teachingAgesExperience!) || 'Experience';
      filters.push({
        id: fieldNames.teachingAgesExperience!,
        label: `Experience: ${experienceLabel}`,
        icon: 'pi pi-star',
        type: 'multiselect',
        removeData: { filterName: fieldNames.teachingAgesExperience! }
      });
    }

    // Student Ages Preference Filter
    if (this.hasValue(request.teacherStudentAgesPreference)) {
      const preferenceLabel = labelResolvers.getFilterLabelFromUrl?.('studentAgesPreference', fieldNames.teacherStudentAgesPreference!) || 'Preference';
      filters.push({
        id: fieldNames.teacherStudentAgesPreference!,
        label: `Preference: ${preferenceLabel}`,
        icon: 'pi pi-heart',
        type: 'multiselect',
        removeData: { filterName: fieldNames.teacherStudentAgesPreference! }
      });
    }

    // Date Range Filter (Approved Date)
    if (this.hasValue(request.approvedDateFrom) || this.hasValue(request.approvedDateTo)) {
      const fromDate = request.approvedDateFrom ? new Date(request.approvedDateFrom).toLocaleDateString('en-US', { dateStyle: 'medium' }) : '';
      const toDate = request.approvedDateTo ? new Date(request.approvedDateTo).toLocaleDateString('en-US', { dateStyle: 'medium' }) : '';
      
      filters.push({
        id: 'dateOfRegistration',
        label: `Approved: ${fromDate} - ${toDate}`,
        icon: 'pi pi-calendar',
        type: 'date',
        removeData: { filterName: 'dateOfRegistration' }
      });
    }

    // Student Ages Range Filter
    if (this.hasRangeFilter(request.studentAgesMin, request.studentAgesMax, 2, 17)) {
      const minAge = request.studentAgesMin || 2;
      const maxAge = request.studentAgesMax || 17;
      
      filters.push({
        id: 'studentAgesRange',
        label: `Ages: ${minAge} - ${maxAge}`,
        icon: 'pi pi-chart-bar',
        type: 'range',
        removeData: { filterName: 'studentAgesRange' }
      });
    }

    return filters;
  }

  /**
   * Generic method to convert any filter object to filter tags
   * Can be extended for other data grid implementations
   */
  convertGenericFiltersToTags<T>(
    filters: T,
    filterConfig: Array<{
      key: keyof T;
      label: string;
      icon?: string;
      type: FilterTagType;
      valueFormatter?: (value: any) => string;
      shouldShow?: (value: any) => boolean;
    }>
  ): IAppliedFilterTag[] {
    const tags: IAppliedFilterTag[] = [];

    filterConfig.forEach(config => {
      const value = filters[config.key];
      const shouldShow = config.shouldShow ? config.shouldShow(value) : this.hasValue(value);

      if (shouldShow) {
        const displayValue = config.valueFormatter ? config.valueFormatter(value) : String(value);
        
        tags.push({
          id: String(config.key),
          label: `${config.label}: ${displayValue}`,
          icon: config.icon,
          type: config.type,
          removeData: { filterName: config.key }
        });
      }
    });

    return tags;
  }

  /**
   * Checks if a value should be considered as an active filter
   */
  private hasValue(value: any): boolean {
    return value !== null && 
           value !== undefined && 
           value !== '' && 
           value !== '0' && 
           value !== 'false' && 
           value !== false &&
           value !== 0;
  }

  /**
   * Checks if sort filter is different from default
   */
  private hasSortFilter(request: IGetTeachersRequest, defaultColumn?: string, defaultDirection?: string): boolean {
    if (!defaultColumn || !defaultDirection) return false;
    
    return !!(request.sortColumn && 
             (request.sortColumn !== defaultColumn || request.sortDirection !== defaultDirection));
  }

  /**
   * Checks if range filter is different from default range
   */
  private hasRangeFilter(min?: number | null, max?: number | null, defaultMin = 2, defaultMax = 17): boolean {
    const hasMin = min !== null && min !== undefined && min !== defaultMin;
    const hasMax = max !== null && max !== undefined && max !== defaultMax;
    return hasMin || hasMax;
  }
}
