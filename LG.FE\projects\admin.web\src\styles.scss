@use "@styles/styles.scss"; 

// for signin page
body.student-screen {
    --animation-speed: 30s;
    /* Add other specific styles for wide screens */
  }
  
  // for signin page
  body.student-bg {
    background-size: cover;
  }
  
  body.bg-parent {
    background-position: 100% 10%;
    background-size: cover;
    background-repeat: no-repeat;
    background-color: #1b2947;
  }
  
  .register-parent {
    background-color: #10132e;
  }
  
  .btn-style {
    line-height: 32px;
    padding: 4px 18px;
  }

form {
  font-size: 14px;

  p-select, .p-select-label {
    margin:0!important;
    padding:0!important;
  }
    input[type="text"], input[type="email"], input[type="password"], input[type="number"], textarea,
    select,
    p-select,.p-select-label,.p-multiselect-label,
    .p-dropdown, .p-multiselect, .p-datepicker {
      border-radius: 0.25rem;
      line-height: 2.375;
      padding-block: 0.475rem;
      font-size: 0.875rem;
      margin: 0!important;
      padding: 0px 5px 0px 5px!important;
      padding-block: 0!important;
    }

    .submit-button {

    }

    .surface-card {
    background-color: var(--surface-card) !important;
    padding: 1rem; /* p-3 equivalent */
    box-shadow: none; /* shadow-0 equivalent */
    border: 1px solid #e5e7eb; /* border-1 border-gray-200 equivalent */
    border-radius: var(--border-radius) !important; /* border-round equivalent */
    margin-bottom: 0.5rem; /* mb-2 equivalent */
    }

    .section-header {
      font-size: 1.25rem; /* text-xl equivalent */
      font-weight: 500; /* font-medium equivalent */
      color: var(--surface-900); /* text-900 equivalent */
      margin-bottom: 0.75rem; /* mb-3 equivalent */
  }

  .p-fileupload-file {
    padding: 0.875rem 12px !important;
  }

  .p-fileupload-file-list {
  }

  .p-fileupload-content {
    gap:0;
    padding: 0;
  }

  .p-progressbar {
    display: none!important;
  }
    .p-fileupload-header, .drop-files {
      margin-bottom: 0!important;
      padding: 0.5rem 12px!important;
    }

    .p-datepicker-input {
      width: 100%!important;
    }
    .p-datepicker {
      padding: 0!important;
    }

    .p-select-label {
      padding:0!important;
    }
    label {
      display: block;
      color: var(--blueberry)!important;
      margin-top: 0!important;
      margin-bottom: 0.675rem!important;
    }

    .p-message-content {
      padding: 9px 11px 8px 6px !important;
    }

    .p-message-text {
      font-size: 0.875rem!important;
    }

    ul.error-list {
      list-style-type: none;
      padding: 0;
    }
    
    ul.error-list li::before {
      content: "•"; 
      color: red; 
      font-size: 1.4rem;
      height: 1rem;
      display: flex;
      align-items: center;
      margin-right: 8px;
    }
}


  /* Target the header cells (<th>) within the p-table */
  .p-datatable .p-datatable-thead > tr > th,
  .p-datatable .p-datatable-tfood > tr > th,
  .p-datatable .p-datatable-thead > app-data-grid-header-footer > tr > th,
  .p-datatable .p-datatable-tfood > app-data-grid-header-footer > tr > th {
    font-size: 0.875rem; /* Adjust this value to make the font smaller */
    padding: 0.75rem 0.5rem; /* Adjust padding if needed to reduce height */
    font-weight: 600;
  }

  /* Overall Datatable Wrapper */
.p-datatable {
  border-radius: 6px; /* Example: {border.radius.md} equivalent */
  border: 1px solid var(--content-border-color);
  background: var(--content-background);
  color: var(--text-color);
  /* Add any other global table styles here */
}

/* Datatable Header Cells - Support both direct and component-wrapped headers */
.p-datatable .p-datatable-thead > tr > th,
.p-datatable .p-datatable-tfoot > tr > th,
.p-datatable .p-datatable-thead > app-data-grid-header-footer > tr > th,
.p-datatable .p-datatable-tfoot > app-data-grid-header-footer > tr > th,
.p-datatable .p-datatable-thead th,
.p-datatable .p-datatable-tfoot th {
  padding: 0.6rem 0.8rem; /* Compact padding */
  background: var(--surface-50); /* Light background */
  color: var(--text-color);
  font-weight: 600; /* Medium-bold font */
  font-size: 0.8125rem; /* Small font */
  border-bottom: 1px solid var(--content-border-color); /* Thin bottom border */
  text-align: start; /* Standard alignment */
}

/* Datatable Body Rows */
.p-datatable .p-datatable-tbody > tr {
  border-bottom: 1px solid var(--content-border-color); /* Thin horizontal row dividers */
  color: var(--text-color);
}

.p-datatable .p-datatable-tbody > tr:hover {
  background: var(--surface-50); /* Subtle hover background */
  color: var(--text-hover-color);
}

.p-datatable .p-datatable-tbody > tr.p-highlight {
  background: var(--highlight-background);
  color: var(--highlight-color);
}

/* Datatable Body Cells */
.p-datatable .p-datatable-tbody > tr > td {
  padding: 0.5rem 0.8rem; /* Very compact cell padding */
  font-size: 0.75rem; /* Very small font size for data */
  color: var(--text-color);
}

/* Datatable Footer Cells */
.p-datatable .p-datatable-tfoot > tr > td,
.p-datatable .p-datatable-tfoot > app-data-grid-header-footer > tr > th {
  padding: 0.6rem 0.8rem; /* Compact padding */
  background: var(--surface-50); /* Light background */
  color: var(--text-muted-color);
  font-weight: 500;
  font-size: 0.75rem;
  border-top: 1px solid var(--content-border-color); /* Thin top border */
}

/* Paginator */
.p-paginator {
  padding: 0.4rem 0.6rem; /* Very compact paginator */
  background: var(--surface-0);
  color: var(--text-muted-color);
}

.p-paginator .p-paginator-page {
  border-radius: 2px; /* Smallest border radius */
  font-size: 0.7rem; /* Very small font */
}

.p-paginator .p-paginator-chip {
  border-radius: 2px;
  font-size: 0.7rem;
  padding: 0.2rem 0.4rem;
}

 /* Style the p-sorticon container */
 sorticon {
  display: inline-block; /* Ensure it behaves well with margin */
  margin-left: 0.25rem; /* Adjust spacing between text and icon */
  vertical-align: middle; /* Align the icon vertically with the text */
}

/* Target the SVG directly within the p-sorticon */
.p-iconwrapper svg {
  width: 0.675rem; /* Set explicit width for the SVG */
  height: 0.675rem; /* Set explicit height for the SVG */
  vertical-align: middle; /* Ensure vertical alignment */
}
  /* Optional: If you want to make the content cells smaller too */
  .p-datatable .p-datatable-tbody > tr > td {
    font-size: 0.875rem; /* Smaller font for data cells */
    padding: 0.5rem 0.5rem; /* Adjust padding */
  }

  /* Adjust styles for the pagination text if needed */
  .p-paginator .p-paginator-current {
    font-size: 0.875rem;
  }

  .p-paginator .p-dropdown .p-dropdown-label {
    font-size: 0.875rem;
  }


  

  .table-container {
    width: 100%;

    ::ng-deep {
      .p-datatable {
        width: 100%;

        // This is the key fix that's still needed
        table {
          display: inline-block;
          width: 1px; // Forces table to size to content
          min-width: 100%; // Ensures table is at least as wide as its container
          text-align: left;
        }

        // Basic row striping
        .p-datatable-tbody > tr:nth-child(odd) {
          background-color: rgba(0, 0, 0, 0.02);
        }

        /* Hover styles for all cells */
        .p-datatable-tbody > tr:hover > td {
          background-color: rgba(95, 72, 150, 0.05) !important;
        }

        .p-datatable-tbody > tr {
          height: auto; // Allow row to grow with content
        }

        .p-datatable-header {
          padding: 0px 0 8px 0px;
          margin: 0;
          background: transparent;
          border: 0;
        }

        .p-datatable-tbody > tr > td {
          white-space: normal !important; // Allow text to wrap
          padding: 8px 15px !important;
          height: auto !important;
          align-content: center;
        }

        /* Support both direct headers and reusable component headers */
        .p-datatable-thead > tr > th,
        .p-datatable-tfoot > tr > th,
        .p-datatable-thead > app-data-grid-header-footer > tr > th,
        .p-datatable-tfoot > app-data-grid-header-footer > tr > th {
          background: #fff;
          color: #000;
          padding: 10px 15px;
          border: 0.8px solid rgb(226, 232, 240);
          border-right-width: 0px;
          border-collapse: separate;
        }

        .p-datatable-thead > tr > th,
        .p-datatable-thead > app-data-grid-header-footer > tr > th {
          border-top: 0;
        }

        /* Key part: Custom styles for frozen column cells */
        td.p-frozen-column[style*="background-color"] {
          &:not(:hover) {
            &:nth-child(odd),
            tr:nth-child(odd) & {
              background-color: rgba(0, 0, 0, 0.02) !important;
            }

            &:nth-child(even),
            tr:nth-child(even) & {
              background-color: #ffffff !important;
            }
          }
        }
      }
    }
  }