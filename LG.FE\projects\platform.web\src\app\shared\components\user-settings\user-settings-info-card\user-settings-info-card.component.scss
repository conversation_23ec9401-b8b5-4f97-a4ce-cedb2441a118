@use "mixins";

:host {
  display: block;
}

// Compact User Info Card
.user-info-card {
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

}

.card-content {
  position: relative;
  z-index: 2;
  padding: 1rem;

  @include mixins.breakpoint(mobile) {
    padding: 0.875rem;
  }
}

// Two Column Layout
.main-layout {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 1.5rem;
  align-items: center;

  @include mixins.breakpoint(mobile) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

.info-column {
  min-width: 0;
  flex: 1;
}

.actions-column {
  flex-shrink: 0;
}

// Compact Profile Section
.profile-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;

  @include mixins.breakpoint(mobile) {
    gap: 0.625rem;
  }
}

.profile-photo-container {
  position: relative;
  flex-shrink: 0;

  ::ng-deep .profile-photo {
    width: 90px !important;
    height: 90px !important;
    border-radius: 100px !important;
    border: 2px solid rgba(255, 255, 255, 0.8) !important;
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.1),
      0 2px 6px rgba(0, 0, 0, 0.06) !important;
    transition: all 0.3s ease !important;

    @include mixins.breakpoint(mobile) {
      width: 60px !important;
      height: 60px !important;
      border-radius: 10px !important;
    }

  }
}

// Compact User Details
.user-details {
  flex: 1;
  min-width: 0;
}

.user-name-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;

  @include mixins.breakpoint(mobile) {
    gap: 0.375rem;
    margin-bottom: 0.375rem;
  }
}

.user-name {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  line-height: 1.2;

  @include mixins.breakpoint(mobile) {
    font-size: 1rem;
  }
}

.user-role-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 12px;
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: #6366f1;

  @include mixins.breakpoint(mobile) {
    padding: 0.1rem 0.375rem;
    font-size: 0.6875rem;
  }

  i {
    font-size: 0.75rem;

    @include mixins.breakpoint(mobile) {
      font-size: 0.6875rem;
    }
  }
}

// Compact User Info
.user-info-compact {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;

  @include mixins.breakpoint(mobile) {
    gap: 0.1875rem;
  }
}

.info-item-compact {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8125rem;
  color: #64748b;

  @include mixins.breakpoint(mobile) {
    gap: 0.375rem;
    font-size: 0.75rem;
  }

  i {
    font-size: 0.75rem;
    color: #6366f1;
    width: 12px;
    flex-shrink: 0;

    @include mixins.breakpoint(mobile) {
      font-size: 0.6875rem;
      width: 10px;
    }
  }

  span {
    font-weight: 500;
    line-height: 1.3;
  }
}

// Compact Actions Column - Enhanced for Standardized Buttons
.actions-grid-compact {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  min-width: 200px;

  @include mixins.breakpoint(mobile) {
    min-width: auto;
    gap: 0.5rem;
  }
}

// Modern Action Buttons using Standardized Button System
.action-button-modern {
  // Override default button width to fit content properly
  width: 100% !important;
  justify-content: flex-start !important;
  text-align: left !important;
  min-width: 200px;
  position: relative;

  @include mixins.breakpoint(mobile) {
    min-width: auto;
    padding: 0.5rem 0.75rem !important;
    font-size: 0.75rem !important;
  }

  // Ensure proper icon and text spacing
  i {
    font-size: 1rem;
    flex-shrink: 0;
    margin-right: 0.5rem;

    @include mixins.breakpoint(mobile) {
      font-size: 0.875rem;
      margin-right: 0.375rem;
    }
  }

  span {
    white-space: nowrap;
    // overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
  }

  // Enhanced hover effect for better user feedback
  &:hover:not(:disabled) {

    // Add subtle shine effect
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
      z-index: 1;
    }
  }

  &:hover::before {
    left: 100%;
  }

  &:active:not(:disabled) {
  }
}

// Compact Skeleton Loading State
.user-info-card-skeleton {
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 16px;
  overflow: hidden;
  animation: skeleton-shimmer 1.5s ease-in-out infinite;

  @include mixins.breakpoint(mobile) {
    border-radius: 12px;
  }
}

.skeleton-content {
  padding: 1rem;

  @include mixins.breakpoint(mobile) {
    padding: 0.875rem;
  }
}

.skeleton-profile {
  display: flex;
  align-items: center;
  gap: 0.75rem;

  @include mixins.breakpoint(mobile) {
    gap: 0.625rem;
  }
}

.skeleton-photo {
  width: 48px;
  height: 48px;
  background: linear-gradient(90deg, #e2e8f0 25%, #f1f5f9 50%, #e2e8f0 75%);
  border-radius: 12px;
  animation: skeleton-wave 1.5s ease-in-out infinite;

  @include mixins.breakpoint(mobile) {
    width: 40px;
    height: 40px;
    border-radius: 10px;
  }
}

.skeleton-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skeleton-line {
  height: 0.875rem;
  background: linear-gradient(90deg, #e2e8f0 25%, #f1f5f9 50%, #e2e8f0 75%);
  border-radius: 4px;
  animation: skeleton-wave 1.5s ease-in-out infinite;

  &.skeleton-name {
    width: 60%;
    height: 1.125rem;
  }

  &.skeleton-info {
    width: 40%;
  }
}

// Animations
@keyframes status-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes skeleton-shimmer {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes skeleton-wave {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}
