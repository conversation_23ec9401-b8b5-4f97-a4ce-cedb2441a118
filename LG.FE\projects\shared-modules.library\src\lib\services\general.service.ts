import {
  Component,
  ComponentRef,
  inject,
  Injectable,
  Injector,
  signal,
  Type,
  ViewChild,
  ViewContainerRef
} from '@angular/core';
import {AbstractControl, FormArray, FormGroup, ValidatorFn, Validators} from '@angular/forms';
import {Location} from '@angular/common';
import {NavigationExtras, Params, Router} from '@angular/router';
import {BehaviorSubject, Observable} from 'rxjs';
import moment from 'moment-timezone';
import {DeviceKind, Dimentions} from '../models/general.model';
import {AuthStateService} from './auth-state.service';
import {Level} from '../models/data';
import {
  IApiResponseBase,
  IBasicProfileInfoDto,
  ICountry,
  IdentityRoutes,
  ILanguageLevelsEnum,
  IStudentGroupDto,
  IStudentLevelEnum
} from '../GeneratedTsFiles';
import {untilDestroyed} from '../helpers/until-destroyed';
import {CustomValidators} from '../helpers/custom-validators';
import {DialogService, DynamicDialogRef} from 'primeng/dynamicdialog';
import {Dialog} from 'primeng/dialog';
import {IEnumDropdownOptions} from '../models/enum-dropdown-options.model';
import {IBasedDataGridRequest} from '../GeneratedTsFiles';

export interface GenericPrimeDropdownOption {
  name: string;
  code: number;

  [key: string]: any;
}

export interface GenericPrimeEnumToDropdownOptionsConfig {
  labelProperty: string;
  valueProperty: string;
  excludeKeys?: string[];
  additionalProperties?: {
    [key: string]: (key: string, enumType: Record<string, string | number>) => string | number | boolean | undefined;
  };
}

@Injectable({
  providedIn: 'root'
})
export class GeneralService {
  untilDestroyed = untilDestroyed();
  private previousUrl: string | null = null;

  private deviceKind$: BehaviorSubject<DeviceKind> = new BehaviorSubject<DeviceKind>({
    w576down: false,
    w768down: false,
    w992down: false,
    w1024down: false,
    w1200down: false,
    is1366: false,
    isBig: true
  });
  public readonly deviceKind: Observable<DeviceKind> = this.deviceKind$.asObservable();

  private openDialogSubject = new BehaviorSubject<any>(null);
  openDialog$: Observable<any> = this.openDialogSubject.asObservable();

  uploadingProgress = signal(-1);
  routerLoading = signal(false);
  divLoading = signal<{ isLoading: boolean; targetSelector: string | null, loadingText?: string }>({
    isLoading: false,
    targetSelector: null,
    loadingText: ''
  });

  showDivLoading(selector?: string | null, loadingText?: string) {
    this.divLoading.set({
      isLoading: true,
      targetSelector: selector ? selector : null,
      loadingText: loadingText ? loadingText : ''
    });
  }

  hideDivLoading() {
    this.divLoading.set({isLoading: false, targetSelector: null, loadingText: ''});
  }

  setUploadingProgress(progress: number) {
    this.uploadingProgress.set(progress);
  }

  getUploadingProgress() {
    return this.uploadingProgress();
  }

  miniLayoutSidebar = signal(false);
  cartSidebarVisible = signal(false);
  calendarSidebarVisible = signal(false);
  sidebarVisible = signal(false);
  userPhotoTopbarToggle = signal(false);
  deviceIs = signal<DeviceKind>({
    w576down: false,
    w768down: false,
    w992down: false,
    w1024down: false,
    w1200down: false,
    is1366: false,
    isBig: true
  });

  private componentRef: ComponentRef<any> | null = null;
  // TODO: delete when we have proper backend

  levels = [
    {label: 'Select a Level *', value: null},
    {label: 'No Experience', value: IStudentLevelEnum.NoExperience},
    {label: 'Beginner', value: IStudentLevelEnum.Beginner},
    {label: 'Intermediate', value: IStudentLevelEnum.Intermediate},
    {label: 'Advanced', value: IStudentLevelEnum.Advanced}
  ];

  dummyLanguages = [
    {name: 'Spanish', image: '/assets/images/icons/languages/spanish-planet-copy.svg', code: 'es'},
    {name: 'japanese', image: '/assets/images/icons/languages/japanese-planet-copy.svg', code: 'jp'},
    {name: 'Greek', image: '/assets/images/icons/languages/greek-planet-copy.svg', code: 'gr'},
    {name: 'Chinese', image: '/assets/images/icons/languages/chinese-planet-copy.svg', code: 'ch'},
    {name: 'French', image: '/assets/images/icons/languages/french-planet-copy.svg', code: 'fr'},
    {name: 'Italian', image: '/assets/images/icons/languages/italian-planet-copy.svg', code: 'it'},
  ];

  dummyStudents = [
    {
      name: 'Student 1',
      isGroup: false,
      id: 1,
      image: 'assets/images/dummy/astronaut-01.png',
    },
    {
      name: 'Student 2',
      isGroup: false,
      id: 2,
      image: 'assets/images/dummy/astronaut-02.png',
    },
    {
      name: 'Student 3',
      isGroup: false,
      id: 3,
      image: 'assets/images/dummy/dummy-img-3.png',
    },
    {
      name: 'English Group',
      isGroup: true,
      id: 4,
      image: 'assets/images/dummy/astronaut-01.png',
    },
  ];

  dummyLevels = [
    {name: 'A1', code: 'A1'},
    {name: 'A2', code: 'A2'},
    {name: 'B1', code: 'B1'},
    {name: 'B2', code: 'B2'},
    {name: 'C1', code: 'C1'},
    {name: 'C2', code: 'C2'},
  ];

  dummyNotifications = [
    {
      "id": 15,
      "type": "package-expired",
      "severity": "High",
      "title": " Package Expired",
      "icon": "pi pi-credit-card",
      "dateCreated": "2023-10-05T11:00:00Z",
      "paragraphText": "Your package for English A1 has expired",
      "isRead": false
    },
    {
      "id": 1,
      "type": "package",
      "severity": "High",
      "title": "Package Expiring Soon",
      "icon": "pi pi-box",
      "dateCreated": "2023-10-05T14:30:00Z",
      "paragraphText": "Your package English A1 with Nickos is expiring on 12 December 2025",
      "isRead": false
    },
    {
      "id": 2,
      "type": "lesson",
      "severity": "Normal",
      "title": "New Lesson Available",
      "icon": "pi pi-book",
      "dateCreated": "2023-10-04T09:15:00Z",
      "paragraphText": "Lesson 5: Advanced Grammar has been added to your course",
      "isRead": true
    },
    {
      "id": 3,
      "type": "library",
      "severity": "Low",
      "title": "Library Update",
      "icon": "pi pi-building",
      "dateCreated": "2023-10-03T16:45:00Z",
      "paragraphText": "New study materials have been added to your library",
      "isRead": false
    },
    {
      "id": 4,
      "type": "package",
      "severity": "High",
      "title": "Payment Required",
      "icon": "pi pi-credit-card",
      "dateCreated": "2023-10-05T11:00:00Z",
      "paragraphText": "Your premium package renewal requires payment confirmation",
      "isRead": false
    },
    {
      "id": 54,
      "type": "availability",
      "severity": "Normal",
      "title": "Availability Reminder",
      "icon": "pi pi-book",
      "dateCreated": "2023-10-04T09:15:00Z",
      "paragraphText": "You have a lesson scheduled for tomorrow at 10:00",
      "isRead": true
    },
    {
      "id": 21,
      "type": "lesson",
      "severity": "Normal",
      "title": "Homework Reminder",
      "icon": "pi pi-calendar",
      "dateCreated": "2023-10-05T08:30:00Z",
      "paragraphText": "Homework for Lesson 4 is due tomorrow",
      "isRead": true
    }
  ]

  lessons = signal([{
    id: '12345',
    name: 'French C1',
    teacher: 'John Daskalopoulos',
    day: 'Monday 11 April',
    time: '8:00 - 9:00',
    location: 'London/United Kingdom (GMT+1:00)',
    status: 'Scheduled',
    duration: '60 mins',
  },
    {
      id: '123456',
      name: 'French C1',
      teacher: 'John Daskalopoulos',
      day: 'Monday 11 April',
      time: '14:00 - 15:00',
      location: 'London/United Kingdom (GMT+1:00)',
      status: 'completed',
      duration: '60 mins',
    }, {
      id: '1234545',
      name: 'German A1 Trial',
      teacher: 'John Daskalopoulos',
      day: 'Monday 11 April',
      time: '11:00 - 12:00',
      location: 'London/United Kingdom (GMT+1:00)',
      isTrial: true,
      status: 'trial',
      duration: '60 mins',
    }, {
      id: '123434545',
      name: 'French C1',
      teacher: 'John Daskalopoulos',
      day: 'Monday 11 April',
      time: '16:15 - 17:45',
      location: 'London/United Kingdom (GMT+1:00)',
      status: 'cancelled',
      duration: '60 mins',
    }

  ]);

  dummyLessons = [{
    id: '1234435',
    name: 'French C1',
    teacher: 'John Daskalopoulos',
    day: 'Monday 11 April',
    time: '8:00 - 9:00',
    location: 'London/United Kingdom (GMT+1:00)',
    status: 'arranged',
    duration: '60 mins',
  },
    {
      id: '12345345',
      name: 'French C1',
      teacher: 'John Daskalopoulos',
      day: 'Monday 11 April',
      time: '8:00 - 9:00',
      location: 'London/United Kingdom (GMT+1:00)',
      status: 'completed',
      duration: '60 mins',
    }, {
      id: '12345',
      name: 'French C1',
      teacher: 'John Daskalopoulos',
      day: 'Monday 11 April',
      time: '8:00 - 9:00',
      location: 'London/United Kingdom (GMT+1:00)',
      status: 'trial',
      duration: '60 mins',
    }, {
      id: '12345',
      name: 'French C1',
      teacher: 'John Daskalopoulos',
      day: 'Monday 11 April',
      time: '8:00 - 9:00',
      location: 'London/United Kingdom (GMT+1:00)',
      status: 'cancelled',
      duration: '60 mins',
    }

  ];

  dummyPackagesPricing = [
    {
      name: 'English Package',
      id: 1,
      image: 'assets/images/dummy/astronaut-01.png',
      price: 6,
    },
    {
      name: 'French Package',
      id: 2,
      image: 'assets/images/dummy/astronaut-01.png',
      price: 8,
    },
    {
      name: 'German Package',
      id: 3,
      image: 'assets/images/dummy/astronaut-01.png',
      price: 5,
    },
    {
      name: 'German Package',
      id: 4,
      image: 'assets/images/dummy/astronaut-01.png',
      price: 5,
    },
    {
      name: 'German Package',
      id: 5,
      image: 'assets/images/dummy/astronaut-01.png',
      price: 5,
    },
  ];

  dummyPackages = [
    {
      name: 'English Package',
      id: 1,
      image: 'assets/images/dummy/astronaut-01.png',
      status: 'active',
      lessons: 8,
      totalLessons: 10,
    },
    {
      name: 'French Package',
      id: 2,
      image: 'assets/images/dummy/astronaut-01.png',
      status: 'expiring',
      lessons: 5,
      totalLessons: 40,
    },
    {
      name: 'German Package',
      id: 3,
      image: 'assets/images/dummy/astronaut-01.png',
      status: 'expired',
      lessons: 40,
      totalLessons: 40,
    },
  ]

  studentMenuItems = [
    {
      name: 'Overview',
      id: 1,
      image: 'assets/images/dashboard/menu/telescope_icon.png',
      link: '/dashboard/student/overview',
    },
    {
      name: 'Homework',
      id: 2,
      image: '/assets/images/dashboard/menu/homework_icon.png',
      link: '/dashboard/student/homework',
    },
    {
      name: 'Lessons',
      id: 3,
      image: '/assets/images/dashboard/menu/schedule_icon.png',
      link: '/dashboard/student/lessons',
    },
    {
      name: 'Progress',
      id: 4,
      image: '/assets/images/dashboard/menu/progress_icon.png',
      link: '/dashboard/student/progress',
    },
    // {
    //     name: 'Achievements',
    //     isGroup: true,
    //     id: 5,
    //     image: '/assets/images/dashboard/menu/achievements-menu-icon.svg',
    //     link: '/dashboard/student/overview',
    // },
  ];

  dummyParentStudents = signal({
    "students": [
      {
        "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "firstName": "John",
        "lastName": "Doe",
        "timezoneIana": "America/New_York",
        "timezoneDisplayName": "Eastern Standard Time",
        "languages": [
          {
            "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
            "name": "English",
            "isActive": true,
            "order": 1
          }
        ],
        "packages": [
          {
            "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
            "languageName": "English",
            "levelEnum": 1,
            "totalLessons": 20,
            "remainingLessons": 15,
            "durationInMinutes": 60,
            "packageType": 1,
            "packageStatus": 1,
            "validUntilDate": "2024-06-24T20:28:15.100Z",
            "extensionUntilDate": "2024-06-24T20:28:15.100Z",
            "hasBeenExtended": true
          }
        ]
      },
      {
        "id": "7e85b34e-1234-5678-9123-456789abcdef",
        "firstName": "Jane",
        "lastName": "Smith",
        "timezoneIana": "Europe/London",
        "timezoneDisplayName": "Greenwich Mean Time",
        "languages": [
          {
            "id": "7e85b34e-1234-5678-9123-456789abcdef",
            "name": "French",
            "isActive": true,
            "order": 1
          }
        ],
        "packages": [
          {
            "languageId": "7e85b34e-1234-5678-9123-456789abcdef",
            "languageName": "French",
            "levelEnum": 2,
            "totalLessons": 30,
            "remainingLessons": 25,
            "durationInMinutes": 45,
            "packageType": 2,
            "packageStatus": 1,
            "validUntilDate": "2024-12-31T23:59:59.000Z",
            "extensionUntilDate": "2025-01-31T23:59:59.000Z",
            "hasBeenExtended": false
          }
        ]
      },
      {
        "id": "5a47f1f0-9d34-4567-a456-dd",
        "firstName": "Alice",
        "lastName": "Johnson",
        "timezoneIana": "Asia/Tokyo",
        "timezoneDisplayName": "Japan Standard Time",
        "languages": [
          {
            "id": "5a47f1f0-9d34-4567-a456-1234567890ab",
            "name": "Japanese",
            "isActive": false,
            "order": 2
          }
        ],
        "packages": [
          {
            "languageId": "5a47f1f0-9d34-4567-a456-1234567890ab",
            "languageName": "Japanese",
            "levelEnum": 3,
            "totalLessons": 40,
            "remainingLessons": 40,
            "durationInMinutes": 30,
            "packageType": 3,
            "packageStatus": 2,
            "validUntilDate": "2024-07-01T00:00:00.000Z",
            "extensionUntilDate": "2024-07-15T00:00:00.000Z",
            "hasBeenExtended": true
          }
        ]
      },
      {
        "id": "5a47f1f0-9d34-4567-a456-ss",
        "firstName": "Alice",
        "lastName": "Johnson",
        "timezoneIana": "Asia/Tokyo",
        "timezoneDisplayName": "Japan Standard Time",
        "languages": [
          {
            "id": "5a47f1f0-9d34-4567-a456-1234567890ab",
            "name": "Japanese",
            "isActive": false,
            "order": 2
          }
        ],
        "packages": [
          {
            "languageId": "5a47f1f0-9d34-4567-a456-1234567890ab",
            "languageName": "Japanese",
            "levelEnum": 3,
            "totalLessons": 40,
            "remainingLessons": 40,
            "durationInMinutes": 30,
            "packageType": 3,
            "packageStatus": 2,
            "validUntilDate": "2024-07-01T00:00:00.000Z",
            "extensionUntilDate": "2024-07-15T00:00:00.000Z",
            "hasBeenExtended": true
          }
        ]
      },
      {
        "id": "5a47f1f0-9d34-4567-a456-aa",
        "firstName": "Alice",
        "lastName": "Johnson",
        "timezoneIana": "Asia/Tokyo",
        "timezoneDisplayName": "Japan Standard Time",
        "languages": [
          {
            "id": "5a47f1f0-9d34-4567-a456-1234567890ab",
            "name": "Japanese",
            "isActive": false,
            "order": 2
          }
        ],
        "packages": [
          {
            "languageId": "5a47f1f0-9d34-4567-a456-1234567890ab",
            "languageName": "Japanese",
            "levelEnum": 3,
            "totalLessons": 40,
            "remainingLessons": 40,
            "durationInMinutes": 30,
            "packageType": 3,
            "packageStatus": 2,
            "validUntilDate": "2024-07-01T00:00:00.000Z",
            "extensionUntilDate": "2024-07-15T00:00:00.000Z",
            "hasBeenExtended": true
          }
        ]
      }
    ]
  });

  public readonly mltLanguages: any[] = [
    {name: "English", code: "ENG"},
    {name: "French", code: "FR"},
    {name: "German", code: "GER"},
    {name: "Italian", code: "IT"},
    {name: "Spanish", code: "SP"},
    {name: "Greek", code: "GRE"},
    {name: "Swedish", code: "SW"},
    {name: "Dutch", code: "DUT"},
    {name: "Russian", code: "RUS"},
    {name: "Japanese", code: "JPN"},
    {name: "Korean", code: "KOR"},
    {name: "Chinese", code: "CHI"},
  ];
  mockBanners = [
    {
      title: 'Your Package is expiring on 12 April 2025!',
      message: `English Free trial package has expired for Dimitris`,
      buttonText: 'Extend Package',
      buttonClass: 'trial-btn hover:bg-cyan-300',
      backgroundStyle: 'bg-red-100 border-red-200',
      imageSrc: 'alarm_clock_hour_time_icon.svg'
    },
    {
      title: "Free Trial Expired",
      message: "Your student Nikos didn't join the free trial lesson",
      buttonText: 'Start Trial',
      buttonClass: 'btn-style bg-cyan-600 text-white hover:bg-cyan-300',
      backgroundStyle: 'bg-orange-50 border-orange-200',
      imageSrc: 'bag_student_icon.svg'
    },
    {
      title: "Package Expired",
      message: "Christina’s package French A1 has expired!",
      buttonText: 'Buy package',
      buttonClass: 'btn-style bg-indigo-600 text-white hover:bg-indigo-300',
      backgroundStyle: 'bg-red-200 border-red-200',
      imageSrc: 'support_time_icon.svg'
    }
  ];
  carouselResponsiveOptions: { breakpoint: string; numVisible: number; numScroll: number; }[] = [
    {
      breakpoint: '1199px',
      numVisible: 1,
      numScroll: 1
    },
    {
      breakpoint: '991px',
      numVisible: 1,
      numScroll: 1
    },
    {
      breakpoint: '767px',
      numVisible: 1,
      numScroll: 1
    }
  ];
  routeSpecificErrors = {
    [IdentityRoutes.postLogin]: [
      {key: 'invalidUsernameOrPassword', message: 'Invalid username or password.'},
      {key: 'requiresEmailConfirmation', message: 'Email confirmation is required.'},
      // Add more login error messages here
    ],
    [IdentityRoutes.postRegisterParent]: [
      {key: 'MobileNumber', message: ''},
      // Add more login error messages here
    ],
    [IdentityRoutes.postRegisterStudent]: [
      // Add more login error messages here
    ],
    // Add other routes with their specific error messages here
  };

  countriesRequiringState = [
    'United States',
    'India',
    'Mexico',
    'Brazil',
    'Australia',
    'Canada',
  ];

  private errorData = signal('');

  public readonly mltLevels = [
    // 'Any',
    Level.A1,
    Level.A2,
    Level.B1,
    Level.B2,
    Level.C1,
    Level.C2,
  ];
  location = inject(Location);
  router = inject(Router);
  auth = inject(AuthStateService);
  @ViewChild('dynamicCContainer', {
    read: ViewContainerRef,
    static: true
  }) dynamicComponentContainer: ViewContainerRef | undefined;
  value = signal('');

  public closeDialog$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  public otpClear$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  public otpRequest$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  private refs: DynamicDialogRef[] = [];

  constructor() {
  }

  public setDevice() {
    var w576down = window.innerWidth <= Dimentions.w576;
    var w576up = window.innerWidth >= Dimentions.w576;
    var w768up = window.innerWidth >= Dimentions.w768;
    var w992up = window.innerWidth >= Dimentions.w992;
    var w1024up = window.innerWidth >= Dimentions.w1024;
    var w1366up = window.innerWidth >= Dimentions.w1366;
    var w1500up = window.innerWidth >= Dimentions.w1500;

    var w768down = window.innerWidth <= Dimentions.w768;
    var w992down = window.innerWidth <= Dimentions.w992;
    var w1024 = window.innerWidth <= Dimentions.w1024;
    var w1200 = window.innerWidth <= Dimentions.w1200;
    var w1366 = window.innerWidth <= Dimentions.w1366;
    var w1500 = window.innerWidth <= Dimentions.w1500;
    var isBig = window.innerWidth > Dimentions.w1366;
    this.deviceKind$.next({
      w576down: w576down,
      w768down: w768down,
      w992down: w992down,
      w1024down: w1024,
      w1200down: w1200,
      is1366: w1366,
      w576up: w576up,
      w768up: w768up,
      w992up: w992up,
      w1024up: w1024up,
      w1366up: w1366up,
      w1500up: w1500up,
      isBig: isBig
    });

  }

  setMiniLayoutSidebar(val: boolean) {
    this.miniLayoutSidebar.set(val);
  }

  setCloseDialog(name: boolean) {
    this.closeDialog$.next(name);
  }


  /**
   Opens a dialog box with the specified component, width, and height.
   @param dialogService - The dialog service object.
   @param component - The component to be displayed in the dialog box.
   @param width - The width of the dialog box in pixels or percentage.
   @param dialogData - Optional data to be passed to the dialog box component.
   @param appendDialogActionsTo - Optional ID of an HTML element where the dialog box actions should be appended.
   @param resultHandler - Optional function to handle the result of the dialog box.
   @param height - Optional height of the dialog box.
   */
  public openDialogWithComponent<T extends Component>(
    dialogService: DialogService,
    component: Type<T>,
    width: string | number | undefined,
    dialogData?: Record<string, unknown>,
    appendDialogActionsTo?: string,
    resultHandler?: (result: unknown) => void,
    height?: string
  ): void {
    this.refs.forEach(ref => ref.close());
    this.refs = [];
    let centerX = '50%';
    let centerY = '50%';
    if (appendDialogActionsTo) {
      const element = document.querySelector('#' + appendDialogActionsTo);
      if (element) {
        const rect = element.getBoundingClientRect();
        centerX = (rect.left + 10 + rect.width / 2 - (typeof width === 'number' ? width : 0) / 2) + 'px';
        centerY = (rect.top + window.scrollY) + 'px';
      }
    }

    const defaultWidth = 290;
    const finalWidth = width || defaultWidth;

    const dialogRef = dialogService.open(component, {
      header: '',
      width: this.containsOnlyNumbers(String(finalWidth)) ? `${finalWidth}px` : finalWidth as string,
      height: height ?? 'auto',
      showHeader: false,
      dismissableMask: true,
      modal: true,
      contentStyle: {
        "max-width": "100%",
        "max-height": height ?? "400px",
        "overflow": "auto",
        "border-radius": "10px",
        "padding": "0px",
      },
      style: appendDialogActionsTo ? {'left': centerX, 'top': centerY, 'position': 'fixed'} : {},
      baseZIndex: 10000,
      maskStyleClass: 'transparent-mask',
      data: {dialogData},
    });
    this.refs.push(dialogRef);
    dialogRef.onClose.subscribe((data: unknown) => {
      console.log('Dialog closed with data:', data);
      resultHandler?.(data);
    });
  }

  containsOnlyNumbers(input: string): boolean {
    // Regular expression pattern to match only numbers
    const pattern = /^[0-9]+$/;

    return pattern.test(input);
  }

  isObjectEmpty(obj: unknown): boolean {
    if (!obj) {
      return true;
    }
    return Object.keys(obj).length === 0;
  }

  // Method to get object entries with type safety
  objectEntries<T extends Record<string, unknown>>(obj: T): [keyof T, T[keyof T]][] {
    if (!obj || typeof obj !== 'object') return [];
    return Object.entries(obj) as [keyof T, T[keyof T]][];
  }

  // Type-safe array check
  isArray<T>(value: unknown): value is T[] {
    return Array.isArray(value);
  }

  // Type-safe object check
  isObject<T extends Record<string, unknown>>(value: unknown): value is T {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
  }

  // Format values for display
  formatValue(value: unknown): string {
    if (value === null || value === undefined) return 'N/A';
    return String(value);
  }

  getDisplayLabel(label: string, isRequired: boolean) {
    return isRequired ? label + ' <b class="text-red-500 ng-invalid-required">*</b>' : label;
  }

  navigateTo(url: string, navigationExtra?: NavigationExtras | undefined, queryParams?: { [key: string]: any }) {
    this.router.navigate([url, navigationExtra ? navigationExtra : {}], {
      ...navigationExtra,
      queryParams: queryParams ? queryParams : undefined
    });
  }

  setPreviousUrl(url: string) {
    this.previousUrl = url;
  }

  goBack(): void {
    if (this.previousUrl) {
      this.router.navigateByUrl(this.previousUrl);
    } else {
      this.location.back();
    }
  }

  goToBookLesson(): void {
    this.router.navigate(['/dashboard/book-lesson/choose-details']);
  }

  goToBookLessonChooseDateTime(): void {
    this.router.navigate(['/dashboard/book-lesson/date-time']);
  }

  goToBookLessonConfirm(): void {
    this.router.navigate(['/dashboard/book-lesson/confirm-details']);
  }

  /** Navigates to the free trial form */
  goToFreeTrialForm(): void {
    this.router.navigate(['/dashboard/parent/request-free-trial/reason/free-trial-reason']);
  }

  goToBuyPackage() {
    this.sidebarVisible.set(false);
    this.router.navigate(['/dashboard/buy-package/select-new-package']);
  }

  goToBuyPackageExtension(params?: any) {
    this.sidebarVisible.set(false);
    this.router.navigate(['/dashboard/buy-package/select-package-extension'], {queryParams: {extension: true}});
  }

  /** Navigates to the free trial form */
  goToStudentDetails(studentId: string, tab: string): void {
    const route = '/dashboard/parent/student/' + studentId + '/' + tab;
    this.router.navigate([route]);
  }

  goToLessonDetails(lessonId: string): void {
    this.router.navigate(['/dashboard/' + this.auth.getUserRole().toLowerCase() + '/lessons/details/' + lessonId]);
  }

  loginRoute(mode: string) {
    switch (mode) {
      case 'parent':
        return '/auth/parent';
      case 'teacher':
        return '/auth/teacher';
      case 'student':
        return '/auth/student';
      default:
        return '/auth/login';
    }
    return '/auth/login';
  }

  navigateToLogin(route: string) {
    this.router.navigate(['/signin', route]);
  }

  registerRoute(mode: string) {
    switch (mode) {
      case 'parent':
        return '/auth/register-parent';
      case 'teacher':
        return '/auth/teacher';
      case 'student':
        return '/auth/register-student';
      default:
        return '/auth/register';
    }
  }

  navigateToParentDashboard() {
    this.router.navigate(['/dashboard/parent/overview']);
  }

  navigateToYoungsterDashboard() {
    this.router.navigate(['/dashboard/student/overview']);
  }

  navigateToBuyPackage() {
    this.router.navigate(['/buy-package']);
  }

  handleRouteError(error: IApiResponseBase, route?: string) {
    let errorMessage = '';

    console.log(error);

    if (error === null) {
      errorMessage += 'An unknown error occurred.';
      return;
    }
    // Extract error details
    const statusCode = error.statusCode;
    const messages: string[] = error?.messages || [];
    const formValidationErrors = error?.formValidationErrors || {};
    console.log('errorData', error);

    if (statusCode === null) {
      errorMessage += 'An unknown error occurred.';
    }
    // Handle generic status codes
    switch (statusCode) {
      case 400:
        errorMessage += 'Oops! Something went wrong.<br>';
        break;
      case 500:
        errorMessage += 'Server error. Please try again later.';
        break;
      default:
        errorMessage += 'An unknown error occurred.';
    }

    // Handle route-specific error messages using the object
    if (route && this.routeSpecificErrors[route]) {
      const routeMessages = this.routeSpecificErrors[route] || [];
      for (const {key, message} of routeMessages) {
        // if (errorData[key]) {
        //   errorMessage += message + '\n';
        // }
      }
    }

    console.log(messages);

    // Append backend messages
    if (messages && messages.length > 0) {
      errorMessage += '\n\n';
      messages.forEach(message => {
        errorMessage += `${message}<br>`;
      });
    }


    // Append formValidationErrors
    // for (const key of Object.keys(formValidationErrors)) {
    //   const errors = formValidationErrors[key];
    //   console.log(key, errors);
    //   if (Array.isArray(errors) && errors.length > 0) {
    //     // if more explanation... add: Form Validation Errors for ${key}:
    //     errorMessage += `${errors.join(', ')}. `;
    //   }
    // }

    this.setErrorDataSignal(errorMessage);

    return errorMessage;
  }

  getErrorDataSignal() {
    return this.errorData();
  }

  setErrorDataSignal(val: string) {
    this.errorData.set(val);
  }

  clearErrorDataSignal() {
    this.errorData.set('');
  }

  get errorDataSignal() {
    return this.errorData();
  }

  openComponent(component: unknown, parameters?: unknown) {
    this.openDialogSubject.next({
      component: component,
      parameters: parameters
    }); // Emit values with parameters
  }

  loadDialogComponent<T extends Component>(
    container: ViewContainerRef,
    component?: Type<T>,
    parameters?: unknown
  ): ComponentRef<T> {
    // Clear previous dynamic component, if any
    this.destroyComponent();
    container.clear();

    // Resolve the component factory for the provided component type
    // Create the component and attach it to the view
    const componentRef = container.createComponent(component as Type<T>, {
      injector: Injector.create({
        providers: [
          {provide: 'dialogParameters', useValue: parameters}
        ]
      })
    });

    // Optionally, you can return the component reference or any other data
    return componentRef;
  }

  destroyComponent(): void {
    if (this.componentRef) {
      this.componentRef.destroy();
      this.componentRef = null;
    }
  }

  onSidebarVisibleChange(visible: boolean): void {
    this.sidebarVisible.set(visible);
  }

  isInvalidFormField(form: FormGroup, controlName: string): boolean {
    const control = form.get(controlName);
    return control?.invalid! && (control.dirty || control.touched) as boolean;
  }

  markAllFormFieldsAsTouched(form: FormGroup) {
    Object.keys(form.controls).forEach(controlName => {
      form.get(controlName)?.markAsTouched();
    });
  }

  getGroupDetails(studentGroupItem: IStudentGroupDto, showNames: boolean = true): string {
    console.log(studentGroupItem);
    if (!studentGroupItem) {
      return '';
    }

    const groupLanguageName = studentGroupItem.groupName;
    const groupLevel = studentGroupItem.groupLevel;
    const groupName = `${groupLanguageName} ${this.getILanguageLevelsEnumText(groupLevel, false)}`;

    const studentNames = studentGroupItem.basicProfileInfoDto!
      .map((student: IBasicProfileInfoDto) => `${student.firstName}`)
      .join(', ');

    return `${groupName} Group Class` + (showNames ? ` (${studentNames})` : '');
  }

  getILanguageLevelsEnumText(value: ILanguageLevelsEnum, showNone: boolean = true, noneText: string = ''): string {
    if (value === ILanguageLevelsEnum.None) {
      return showNone ? noneText : '';
    }

    const enumKey = Object.keys(ILanguageLevelsEnum).find(
      key => ILanguageLevelsEnum[key as keyof typeof ILanguageLevelsEnum] === value
    );
    return enumKey ? enumKey : '';
  }

  getUserInitials(user: IBasicProfileInfoDto) {
    return (user.firstName.charAt(0) + user.lastName.charAt(0)).toUpperCase();
  }

  // Method to get image URL based on language name
  getImageUrlForLanguage(languageName: string): string {
    // Convert language name to lowercase and replace spaces with dashes
    const sanitizedLanguageName = languageName.toLowerCase().replace(/\s+/g, '-');
    return `assets/images/flags/${sanitizedLanguageName}-planet-copy.svg`;
  }

  getUserLocalTime(timeZoneIana: string, showDate: boolean = false): string {
    const now = moment(); // Get the current time
    const localTime = now.tz(timeZoneIana); // Convert to the specified time zone

    // Format the output based on the showDate parameter
    if (showDate) {
      return localTime.format('HH:mm - D MMM YYYY'); // Human-readable date format
    }

    return localTime.format('HH:mm'); // Default format to only show time
  }

  /**
   * Calculate age based on the given date of birth
   * @param dateOfBirth - The date of birth in ISO format (e.g., "2015-11-30T22:00:00")
   * @returns The calculated age in years
   */
  calculateAge(dateOfBirth: Date | undefined): number | null {
    if (!dateOfBirth) {
      return null; // Return null or handle invalid input as needed
    }

    const birthDate = moment(dateOfBirth);
    const today = moment();

    return today.diff(birthDate, 'years');
  }

  getDropdownOptionsFromEnum<T extends Record<string, string | number>>(
    enumType: T,
    config: GenericPrimeEnumToDropdownOptionsConfig
  ): GenericPrimeDropdownOption[] {
    return Object.keys(enumType)
      .filter(key => isNaN(Number(key))) // Filter out numeric keys
      .filter(key => !config.excludeKeys?.includes(key)) // Exclude specified keys
      .map(key => {
        // Create the base option object
        let option: GenericPrimeDropdownOption = {
          name: '',
          code: 0,
          [config.labelProperty]: key,
          [config.valueProperty]: enumType[key]
        };

        // Assign name and code explicitly
        option.name = option[config.labelProperty];
        option.code = option[config.valueProperty] as number;

        // Add additional properties if specified
        if (config.additionalProperties) {
          for (const prop in config.additionalProperties) {
            option[prop] = config.additionalProperties[prop](key, enumType);
          }
        }

        return option;
      });
  }

  getLocalTime(timezone: string): string {
    const options: Intl.DateTimeFormatOptions = {
      timeZone: timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false, // Use 24-hour format; change to true for 12-hour format
    };

    const formatter = new Intl.DateTimeFormat([], options);
    const parts = formatter.formatToParts(new Date());

    // Construct the formatted date and time
    const date = parts.find(part => part.type === 'day')?.value + '/' +
      parts.find(part => part.type === 'month')?.value + '/' +
      parts.find(part => part.type === 'year')?.value;

    const time = parts.find(part => part.type === 'hour')?.value + ':' +
      parts.find(part => part.type === 'minute')?.value;

    return `${date} ${time}`;
  }

  getEnumName<T extends { [key: string]: number | string }>(enumObj: T, value: number): string {
    return Object.keys(enumObj).find(key => enumObj[key] === value) || '';
  }

  findInvalidControls(userForm: FormGroup | FormArray, prefix: string = ''): string[] {
    const invalid: string[] = [];

    if (userForm instanceof FormGroup) {
      Object.keys(userForm.controls).forEach((controlName) => {
        const control: AbstractControl = userForm.controls[controlName];
        const fullControlPath = prefix ? `${prefix}.${controlName}` : controlName;

        if (control instanceof FormGroup) {
          const nestedInvalid = this.findInvalidControls(control, fullControlPath);
          invalid.push(...nestedInvalid);
        } else if (control instanceof FormArray) {
          // Check if the FormArray itself has validation errors
          if (control.invalid && control.errors) {
            const displayName = controlName
              .replace(/([A-Z])/g, ' $1')
              .replace(/^./, str => str.toUpperCase())
              .trim();
            const invalidControlName = prefix ? `${prefix}.${displayName}` : displayName;
            invalid.push(invalidControlName);
          }
          
          // Also check individual controls within the FormArray
          control.controls.forEach((arrayControl: AbstractControl, arrayIndex: number) => {
            if (arrayControl instanceof FormGroup) {
              const arrayPath = `${fullControlPath}[${arrayIndex}]`;
              const nestedInvalid = this.findInvalidControls(arrayControl, arrayPath);
              invalid.push(...nestedInvalid);
            }
          });
        } else if (control.invalid) {
          const displayName = controlName
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, str => str.toUpperCase())
            .trim();
          const invalidControlName = prefix ? `${prefix}.${displayName}` : displayName;
          invalid.push(invalidControlName);
        }
      });
    }
    return invalid;
  }


  isStateRequired(country: string): boolean {
    const countriesRequiringState = this.countriesRequiringState;
    return countriesRequiringState.includes(country);
  }

  /**
   * Sets up dynamic validation for a state field based on the country value in a FormGroup.
   * @param formGroup The FormGroup containing 'country' and 'state' controls
   * @param countryControlName The name of the country control (default: 'country')
   * @param stateControlName The name of the state control (default: 'state')
   * @param countriesRequiringState Optional custom list of countries requiring state
   */
  setupDynamicStateValidation(
    formGroup: FormGroup,
    countryControlName: string = 'country',
    stateControlName: string = 'state'
  ): void {
    // Helper function to get nested controls
    const getNestedControl = (group: FormGroup, path: string): AbstractControl | null => {
      return group.get(path);
    };

    const countryControl = getNestedControl(formGroup, countryControlName);
    const stateControl = getNestedControl(formGroup, stateControlName);

    if (countryControl && stateControl) {
      const initialStateValidators: ValidatorFn[] = stateControl.validator
        ? [stateControl.validator]
        : [];

      // Helper to apply validation logic
      const applyStateValidation = (countryValue: ICountry | string) => {
        const countryName = typeof countryValue === 'string' ? countryValue : countryValue?.name || '';
        console.log('Applying validation for country:', countryName);
        if (this.isStateRequired(countryName)) {
          stateControl.setValidators([...initialStateValidators, Validators.required]);
        } else {
          stateControl.setValidators(initialStateValidators);
        }
        stateControl.updateValueAndValidity();
      };

      // Subscribe to country value changes
      countryControl.valueChanges
        .pipe()
        .subscribe((countryValue: ICountry | string) => {
          console.log("🚀 ~ GeneralService ~ countryControl.valueChanges.subscribe ~ countryValue:", countryValue);
          applyStateValidation(countryValue);
        });

      // Trigger initial validation
      const initialCountry = countryControl.value;
      console.log('Initial Country Value:', initialCountry);
      applyStateValidation(initialCountry);
    } else {
      console.warn(`Controls '${countryControlName}' or '${stateControlName}' not found in FormGroup`);
    }
  }

  getFileIconAndColor(contentType: string): { icon: string; color: string } {
    // Default fallback
    const defaultResponse = {icon: 'pi pi-file', color: 'gray'};

    // Mapping of content types to icons and colors
    const fileTypeMap: Record<string, { icon: string; color: string }> = {
      'application/pdf': {icon: 'pi pi-file-pdf', color: 'red'},
      'application/vnd.ms-excel': {icon: 'pi pi-file-excel', color: 'green'},
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': {icon: 'pi pi-file-excel', color: 'green'}, // Excel .xlsx
      'application/msword': {icon: 'pi pi-file-word', color: 'blue'},
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': {
        icon: 'pi pi-file-word',
        color: 'blue'
      }, // Word .docx
      'image/jpeg': {icon: 'pi pi-image', color: 'purple'},
      'image/png': {icon: 'pi pi-image', color: 'purple'},
      'image/gif': {icon: 'pi pi-image', color: 'purple'},
      'text/plain': {icon: 'pi pi-file', color: 'black'},
    };

    // Normalize contentType to lowercase to handle case variations
    const normalizedContentType = contentType?.toLowerCase();

    // Return the mapped icon and color, or default if not found
    return fileTypeMap[normalizedContentType] || defaultResponse;
  }

    /**
     * Converts any enum value to a beautified display text
     * @param enumValue The enum value
     * @param enumType The enum type
     * @returns Formatted display text
     */
    getEnumDisplayText<T extends { [key: string]: string | number }>(
      enumValue: T[keyof T],
      enumType: T
  ): string {
      // Get the enum key from value
      const enumKey = Object.keys(enumType).find(
          key => enumType[key] === enumValue
      );

      if (!enumKey) {
          return 'Unknown';
      }

      // Convert camelCase or PascalCase to space-separated words
      return enumKey
          // Split by capital letters
          .replace(/([A-Z])/g, ' $1')
          // Handle special cases with "By" or "No"
          .replace(/(\s)(By|No)(\s)/g, (_, pre, word, post) => 
              `${pre}${word.toLowerCase()}${post}`)
          // Clean up and capitalize first letter
          .trim()
          .replace(/^\w/, c => c.toUpperCase());
  }

  /**
   * Sanitizes and updates URL query parameters
   * - Removes null, undefined, and empty values
   * - Converts Date objects to ISO strings
   * - Works with any request object that extends IBasedDataGridRequest
   *
   * @param params The object containing parameters to sanitize (typically queryParams)
   * @param options Optional navigation options
   */
  updateQueryParams<T extends IBasedDataGridRequest>(
    params: T,
    options: {
      queryParamsHandling?: 'merge' | 'preserve' | '',
      preserveFragment?: boolean,
      replaceUrl?: boolean  // Add this new option
    } = {},
  ): void {
    const updatedParams: Record<string, any> = {};

    // Only include non-null, non-undefined, and non-empty values in the URL
    for (const [key, value] of Object.entries(params)) {
      if (value !== null && value !== undefined && value !== '') {
        // Handle date objects specially
        if (value instanceof Date) {
          updatedParams[key] = value.toISOString();
        } else {
          updatedParams[key] = value;
        }
      }
    }

    // Navigate with the updated parameters
    this.router.navigate([], {
      queryParams: updatedParams,
      queryParamsHandling: options.queryParamsHandling || '',
      preserveFragment: options.preserveFragment !== undefined ? options.preserveFragment : true,
      replaceUrl: options.replaceUrl || false  // Add support for replaceUrl option
    });
  }


  /**
   * Generic method to convert a flags-based enum value to an array of individual flags
   * @param flagValue The combined flags value to convert
   * @param options Array of option objects containing individual flag values
   * @returns Array of individual flag values that make up the combined value
   */
  convertFlagsToArray(
    flagValue: number,
    options: IEnumDropdownOptions[],
  ): number[] {
    if (!flagValue) return [];

    const result: number[] = [];

    for (const option of options) {
      // Ensure we're working with numbers
      const optionValue = Number(option.value);

      // Skip zero values to avoid incorrect matching
      if (optionValue === 0) continue;

      // Check if this flag is set in the combined value
      if ((flagValue & optionValue) === optionValue) {
        result.push(optionValue);
      }
    }

    return result;
  }

  /**
   * Extracts flag values from query parameters and converts them to arrays
   * @param params The route params object
   * @param paramName The name of the parameter in the URL
   * @param options Array of options for the particular enum
   * @returns Array of flag values
   */
  extractFlagsFromParams(
    params: Params,
    paramName: string,
    options: IEnumDropdownOptions[],
  ): number[] {
    // Check if param exists and has a valid value
    const paramValue = params[paramName];
    if (paramValue == null || paramValue === 'null') {
      return [];
    }

    // Convert to number and process
    const flagValue = Number(paramValue);

    console.log(`Converting flags for ${paramName}: ${flagValue}`);

    // Convert the combined flag value to an array of individual flag values
    const result = this.convertFlagsToArray(flagValue, options);

    console.log(`Result for ${paramName}: ${JSON.stringify(result)}`);

    return result;
  }

  /**
   * Converts an array of flag values back to a single combined flags value
   * @param flagsArray Array of individual flag values
   * @returns Single number representing the combined flags or null if array is empty
   */
  convertArrayToFlags(flagsArray: number[]): number | null {
    if (!flagsArray || flagsArray.length === 0) return null;
    return flagsArray.reduce((result, flag) => result | flag, 0);
  }

}
